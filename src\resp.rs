use serde::{Deserialize, Serialize};
use salvo::prelude::*;

/// 标准API响应结构
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub code: i32,
    pub message: String,
    pub data: Option<T>,
}

impl<T> ApiResponse<T> {
    /// 创建成功响应
    pub fn success(data: T) -> Self {
        Self {
            code: 200,
            message: "Success".to_string(),
            data: Some(data),
        }
    }

    /// 创建成功响应（无数据）
    pub fn success_empty() -> ApiResponse<()> {
        ApiResponse {
            code: 200,
            message: "Success".to_string(),
            data: None,
        }
    }

    /// 创建错误响应
    pub fn error(code: i32, message: String) -> ApiResponse<()> {
        ApiResponse {
            code,
            message,
            data: None,
        }
    }


}

/// 为Salvo Response实现便捷方法的trait
pub trait ResponseExt {
    /// 返回成功响应
    fn success<T: Serialize + Send>(&mut self, data: T);

    /// 返回成功响应（无数据）
    fn success_empty(&mut self);

    /// 返回错误响应
    fn error(&mut self, code: i32, message: String);
}

impl ResponseExt for Response {
    fn success<T: Serialize + Send>(&mut self, data: T) {
        self.status_code(StatusCode::OK);
        self.render(Json(ApiResponse::success(data)));
    }

    fn success_empty(&mut self) {
        self.status_code(StatusCode::OK);
        self.render(Json(ApiResponse::<()>::success_empty()));
    }

    fn error(&mut self, code: i32, message: String) {
        let status_code = match code {
            400 => StatusCode::BAD_REQUEST,
            404 => StatusCode::NOT_FOUND,
            409 => StatusCode::CONFLICT,
            500 => StatusCode::INTERNAL_SERVER_ERROR,
            _ => StatusCode::INTERNAL_SERVER_ERROR,
        };
        self.status_code(status_code);
        self.render(Json(ApiResponse::<()>::error(code, message)));
    }
}

/// 便捷宏，用于快速创建响应
#[macro_export]
macro_rules! api_success {
    ($data:expr) => {
        $crate::resp::ApiResponse::success($data)
    };
}

#[macro_export]
macro_rules! api_error {
    ($code:expr, $msg:expr) => {
        $crate::resp::ApiResponse::error($code, $msg.to_string())
    };
}
