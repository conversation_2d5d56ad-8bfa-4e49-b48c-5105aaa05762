use serde::{Deserialize, Serialize};
use std::sync::OnceLock;
use std::fs;

/// 全局配置实例
static APP_CONFIG: OnceLock<AppConfig> = OnceLock::new();

/// 获取全局配置实例
pub fn get_config() -> &'static AppConfig {
    APP_CONFIG.get().expect("Config not initialized")
}

/// 初始化配置
pub fn init_config() -> Result<(), Box<dyn std::error::Error>> {
    let config = load_config()?;
    APP_CONFIG.set(config).map_err(|_| "Failed to set global config")?;
    Ok(())
}

/// 加载配置
fn load_config() -> Result<AppConfig, Box<dyn std::error::Error>> {
    // 1. 从默认配置开始
    let mut config = AppConfig::default();


    match fs::read_to_string("config.toml") {
        Ok(config_content) => {
            config = toml::from_str(&config_content)?;
            println!("已加载配置文件: config.toml");
        }
        Err(_) => {
            println!("未找到配置文件，使用默认配置");
        }
    }

    // 3. 验证配置
    validate_config(&config)?;

    Ok(config)
}

/// 验证配置
fn validate_config(config: &AppConfig) -> Result<(), Box<dyn std::error::Error>> {
    // 验证端口范围
    if config.server.port == 0 {
        return Err("Port cannot be 0".into());
    }
    
    // 验证分页配置
    if config.pagination.default_page_size > config.pagination.max_page_size {
        return Err("Default page size cannot be larger than max page size".into());
    }
    
    if config.pagination.min_page_size == 0 {
        return Err("Min page size cannot be 0".into());
    }
    
    // 验证目录路径
    if config.storage.papers_directory.is_empty() {
        return Err("Papers directory cannot be empty".into());
    }
    
    // 验证搜索配置
    if config.search.min_word_length == 0 {
        return Err("Min word length cannot be 0".into());
    }
    
    Ok(())
}

/// 应用配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    pub server: ServerConfig,
    pub storage: StorageConfig,
    pub pagination: PaginationConfig,
    pub search: SearchConfig,
    pub stats: StatsConfig,
    pub cache: CacheConfig,
}

/// 服务器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
    pub name: String,
    pub description: String,
}

/// 存储配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageConfig {
    pub papers_directory: String,
    pub allowed_image_formats: Vec<String>,
    pub allowed_document_formats: Vec<String>,
}

/// 分页配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationConfig {
    pub default_page_size: u32,
    pub max_page_size: u32,
    pub min_page_size: u32,
}

/// 搜索配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchConfig {
    pub default_sort_by: String,
    pub default_sort_order: String,
    pub min_word_length: usize,
}

/// 统计配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StatsConfig {
    pub top_authors_limit: usize,
    pub top_keywords_limit: usize,
    pub recent_papers_limit: usize,
}

/// 缓存配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheConfig {
    pub verbose_logging: bool,
}

/// 默认配置实现
impl Default for AppConfig {
    fn default() -> Self {
        Self {
            server: ServerConfig::default(),
            storage: StorageConfig::default(),
            pagination: PaginationConfig::default(),
            search: SearchConfig::default(),
            stats: StatsConfig::default(),
            cache: CacheConfig::default(),
        }
    }
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            host: "127.0.0.1".to_string(),
            port: 3000,
            name: "Lite Papers".to_string(),
            description: "轻量级科研文献管理系统".to_string(),
        }
    }
}

impl Default for StorageConfig {
    fn default() -> Self {
        Self {
            papers_directory: "papers".to_string(),
            allowed_image_formats: vec![
                "jpg".to_string(), "jpeg".to_string(), "png".to_string(),
                "gif".to_string(), "bmp".to_string(), "webp".to_string(), "svg".to_string()
            ],
            allowed_document_formats: vec![
                "pdf".to_string(), "txt".to_string(), "md".to_string(), "json".to_string()
            ],
        }
    }
}

impl Default for PaginationConfig {
    fn default() -> Self {
        Self {
            default_page_size: 10,
            max_page_size: 100,
            min_page_size: 1,
        }
    }
}

impl Default for SearchConfig {
    fn default() -> Self {
        Self {
            default_sort_by: "created_at".to_string(),
            default_sort_order: "desc".to_string(),
            min_word_length: 2,
        }
    }
}

impl Default for StatsConfig {
    fn default() -> Self {
        Self {
            top_authors_limit: 10,
            top_keywords_limit: 20,
            recent_papers_limit: 10,
        }
    }
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            verbose_logging: true,
        }
    }
}

/// 配置工具方法
impl AppConfig {
    /// 获取服务器监听地址
    pub fn get_server_address(&self) -> String {
        format!("{}:{}", self.server.host, self.server.port)
    }
}
