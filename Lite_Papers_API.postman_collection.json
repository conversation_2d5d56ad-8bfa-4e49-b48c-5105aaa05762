{"info": {"name": "Lite Papers API", "description": "轻量级科研文献管理系统 API 集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://127.0.0.1:3000", "type": "string"}, {"key": "paperId", "value": "", "type": "string"}], "item": [{"name": "系统管理", "item": [{"name": "健康检查", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/health", "host": ["{{baseUrl}}"], "path": ["health"]}}}, {"name": "缓存统计", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/papers/cache/stats", "host": ["{{baseUrl}}"], "path": ["api", "papers", "cache", "stats"]}}}, {"name": "重新加载缓存", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/papers/cache/reload", "host": ["{{baseUrl}}"], "path": ["api", "papers", "cache", "reload"]}}}]}, {"name": "文献管理", "item": [{"name": "获取文献列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/papers?page=1&page_size=10", "host": ["{{baseUrl}}"], "path": ["api", "papers"], "query": [{"key": "page", "value": "1"}, {"key": "page_size", "value": "10"}, {"key": "folder", "value": "", "disabled": true}]}}}, {"name": "创建文献", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Attention Is All You Need\",\n  \"authors\": [\"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON><PERSON><PERSON>\", \"<PERSON><PERSON>\"],\n  \"journal\": \"NIPS\",\n  \"year\": 2017,\n  \"doi\": \"10.48550/arXiv.1706.03762\",\n  \"abstract_text\": \"The dominant sequence transduction models are based on complex recurrent or convolutional neural networks...\",\n  \"keywords\": [\"transformer\", \"attention\", \"neural networks\"],\n  \"folder_path\": \"AI Papers\"\n}"}, "url": {"raw": "{{baseUrl}}/api/papers", "host": ["{{baseUrl}}"], "path": ["api", "papers"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 201) {", "    const response = pm.response.json();", "    pm.collectionVariables.set('paperId', response.data.id);", "}"]}}]}, {"name": "获取文献详情", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/papers/{{paperId}}", "host": ["{{baseUrl}}"], "path": ["api", "papers", "{{paperId}}"]}}}, {"name": "更新文献", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"Updated Title\",\n  \"keywords\": [\"updated\", \"keywords\"]\n}"}, "url": {"raw": "{{baseUrl}}/api/papers/{{paperId}}", "host": ["{{baseUrl}}"], "path": ["api", "papers", "{{paperId}}"]}}}, {"name": "删除文献", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/papers/{{paperId}}", "host": ["{{baseUrl}}"], "path": ["api", "papers", "{{paperId}}"]}}}]}, {"name": "搜索功能", "item": [{"name": "全文搜索", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/papers/search?q=transformer", "host": ["{{baseUrl}}"], "path": ["api", "papers", "search"], "query": [{"key": "q", "value": "transformer"}]}}}, {"name": "复合搜索", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/papers/search?q=attention&author=vaswani&year_start=2017&year_end=2020&sort_by=year&sort_order=desc", "host": ["{{baseUrl}}"], "path": ["api", "papers", "search"], "query": [{"key": "q", "value": "attention"}, {"key": "author", "value": "v<PERSON><PERSON><PERSON>"}, {"key": "year_start", "value": "2017"}, {"key": "year_end", "value": "2020"}, {"key": "sort_by", "value": "year"}, {"key": "sort_order", "value": "desc"}]}}}, {"name": "按文件夹搜索", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/papers/search?folder=AI Papers&page=1&page_size=20", "host": ["{{baseUrl}}"], "path": ["api", "papers", "search"], "query": [{"key": "folder", "value": "AI Papers"}, {"key": "page", "value": "1"}, {"key": "page_size", "value": "20"}]}}}]}, {"name": "文件管理", "item": [{"name": "获取文件列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/papers/{{paperId}}/files", "host": ["{{baseUrl}}"], "path": ["api", "papers", "{{paperId}}", "files"]}}}, {"name": "上传原始文件", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/api/papers/{{paperId}}/files?type=origin", "host": ["{{baseUrl}}"], "path": ["api", "papers", "{{paperId}}", "files"], "query": [{"key": "type", "value": "origin"}]}}}, {"name": "上传笔记文件", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/api/papers/{{paperId}}/files?type=note", "host": ["{{baseUrl}}"], "path": ["api", "papers", "{{paperId}}", "files"], "query": [{"key": "type", "value": "note"}]}}}, {"name": "下载文件", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/papers/{{paperId}}/files/example.pdf", "host": ["{{baseUrl}}"], "path": ["api", "papers", "{{paperId}}", "files", "example.pdf"]}}}, {"name": "预览文件", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/papers/{{paperId}}/files/preview/example.pdf", "host": ["{{baseUrl}}"], "path": ["api", "papers", "{{paperId}}", "files", "preview", "example.pdf"]}}}, {"name": "删除文件", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/papers/{{paperId}}/files/example.pdf", "host": ["{{baseUrl}}"], "path": ["api", "papers", "{{paperId}}", "files", "example.pdf"]}}}]}, {"name": "文件夹管理", "item": [{"name": "获取文件夹列表", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/folders", "host": ["{{baseUrl}}"], "path": ["api", "folders"]}}}, {"name": "创建文件夹", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Deep Learning\",\n  \"parent_path\": \"AI Papers\"\n}"}, "url": {"raw": "{{baseUrl}}/api/folders", "host": ["{{baseUrl}}"], "path": ["api", "folders"]}}}, {"name": "重命名文件夹", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"new_name\": \"Machine Learning\"\n}"}, "url": {"raw": "{{baseUrl}}/api/folders/Deep Learning", "host": ["{{baseUrl}}"], "path": ["api", "folders", "Deep Learning"]}}}, {"name": "删除文件夹", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/api/folders/Deep Learning", "host": ["{{baseUrl}}"], "path": ["api", "folders", "Deep Learning"]}}}]}, {"name": "统计分析", "item": [{"name": "详细统计信息", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/papers/stats/detailed", "host": ["{{baseUrl}}"], "path": ["api", "papers", "stats", "detailed"]}}}, {"name": "文件夹树结构", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/papers/folders/tree", "host": ["{{baseUrl}}"], "path": ["api", "papers", "folders", "tree"]}}}]}]}