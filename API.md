# Lite Papers API 文档

## 概述

Lite Papers 是一个轻量级的学术文献管理系统，提供高性能的文献存储、搜索和管理功能。

## 基础信息

- **基础URL**: `http://127.0.0.1:3000`
- **响应格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "code": 200,
  "message": "Success",
  "data": {...}
}
```

## 健康检查

### GET /health

获取系统健康状态和基本统计信息。

**响应示例**:
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "status": "healthy",
    "cache_loaded": true,
    "papers_count": 3,
    "folders_count": 1,
    "uptime_seconds": 16,
    "timestamp": "2025-06-11T05:18:43.771083+00:00"
  }
}
```

## 文献管理

### GET /api/papers

获取文献列表（分页）。

**查询参数**:
- `folder` (可选): 文件夹路径过滤
- `page` (可选): 页码，默认1
- `page_size` (可选): 每页大小，默认10

### POST /api/papers

创建新文献。

### GET /api/papers/{id}

根据ID获取单个文献。

### PUT /api/papers/{id}

更新文献信息。

### DELETE /api/papers/{id}

删除文献。

## 搜索功能

### GET /api/papers/search

通用搜索接口。

**查询参数**:
- `q` (可选): 全文搜索关键词
- `title` (可选): 标题搜索
- `author` (可选): 作者搜索
- `keyword` (可选): 关键词搜索
- `abstract` (可选): 摘要搜索
- `folder` (可选): 文件夹路径

### GET /api/papers/search/title

按标题搜索文献。

**查询参数**:
- `keyword` (必需): 搜索关键词

### GET /api/papers/search/author

按作者搜索文献。

**查询参数**:
- `author` (必需): 作者姓名

### GET /api/papers/search/paginated

分页搜索接口。

**查询参数**:
- 支持所有搜索参数
- `page` (可选): 页码，默认1
- `page_size` (可选): 每页大小，默认10

## 统计分析

### GET /api/papers/cache/stats

获取缓存统计信息。

### GET /api/papers/stats/detailed

获取详细统计信息。

### GET /api/papers/folders/tree

获取文件夹树结构。

## 缓存管理

### POST /api/papers/cache/reload

重新加载缓存。

## 文件管理

### GET /api/papers/{id}/files

获取文献的文件列表。

### POST /api/papers/{id}/files

上传文件到文献。

### GET /api/papers/{id}/files/{filename}

下载文献文件。

### DELETE /api/papers/{id}/files/{filename}

删除文献文件。

### GET /api/papers/{id}/files/preview/{filename}

预览文献文件。

## 文件夹管理

### GET /api/folders

获取文件夹列表。

### POST /api/folders

创建文件夹。

### DELETE /api/folders/{**path}

删除文件夹。

### PUT /api/folders/{**path}

重命名文件夹。

## 性能监控

所有API响应都包含性能头信息：
- `X-Response-Time`: 请求处理时间（毫秒）

## 错误处理

系统使用标准HTTP状态码：
- `200`: 成功
- `400`: 请求错误
- `404`: 资源不存在
- `500`: 服务器内部错误

错误响应格式：
```json
{
  "code": 400,
  "message": "Error description",
  "data": null
}
```
