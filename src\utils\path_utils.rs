use std::path::{Path, PathBuf};
use crate::error::AppResult;

/// 统一的路径处理工具
/// 消除各处重复的路径计算逻辑
pub struct PathUtils;

impl PathUtils {
    /// 计算相对于基础目录的路径
    pub fn calculate_relative_path(path: &Path, base_dir: &Path) -> String {
        if let Ok(relative) = path.strip_prefix(base_dir) {
            relative.to_string_lossy().to_string()
        } else {
            // 如果无法计算相对路径，返回文件夹名称
            path.file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("")
                .to_string()
        }
    }

    /// 计算相对于papers目录的路径
    pub fn calculate_papers_relative_path(path: &Path) -> String {
        let config = crate::config::get_config();
        let papers_dir = PathBuf::from(&config.storage.papers_directory);
        Self::calculate_relative_path(path, &papers_dir)
    }

    /// 获取papers目录的绝对路径
    pub fn get_papers_directory() -> PathBuf {
        let config = crate::config::get_config();
        PathBuf::from(&config.storage.papers_directory)
    }

    /// 构建papers目录下的子路径
    pub fn join_papers_path(sub_path: &str) -> PathBuf {
        let papers_dir = Self::get_papers_directory();
        if sub_path.is_empty() {
            papers_dir
        } else {
            papers_dir.join(sub_path)
        }
    }

    /// 验证路径是否在papers目录内（安全检查）
    pub fn is_within_papers_directory(path: &Path) -> bool {
        let papers_dir = Self::get_papers_directory();
        path.starts_with(&papers_dir)
    }

    /// 规范化文件夹路径（移除多余的分隔符等）
    pub fn normalize_folder_path(path: &str) -> String {
        if path.is_empty() {
            return String::new();
        }

        // 移除开头和结尾的斜杠
        let trimmed = path.trim_start_matches('/').trim_end_matches('/');
        
        // 规范化路径分隔符
        let normalized = trimmed.replace('\\', "/");
        
        // 移除重复的斜杠
        let parts: Vec<&str> = normalized.split('/').filter(|s| !s.is_empty()).collect();
        parts.join("/")
    }

    /// 获取文件夹的父路径
    pub fn get_parent_folder_path(folder_path: &str) -> Option<String> {
        let normalized = Self::normalize_folder_path(folder_path);
        if normalized.is_empty() {
            return None;
        }

        let parts: Vec<&str> = normalized.split('/').collect();
        if parts.len() <= 1 {
            Some(String::new()) // 父路径是根目录
        } else {
            Some(parts[..parts.len() - 1].join("/"))
        }
    }

    /// 检查是否是子文件夹路径
    pub fn is_subfolder_of(child_path: &str, parent_path: &str) -> bool {
        let child_normalized = Self::normalize_folder_path(child_path);
        let parent_normalized = Self::normalize_folder_path(parent_path);

        if parent_normalized.is_empty() {
            // 根目录是所有路径的父目录
            return true;
        }

        child_normalized.starts_with(&format!("{}/", parent_normalized))
    }

    /// 获取文件夹名称（路径的最后一部分）
    pub fn get_folder_name(folder_path: &str) -> String {
        let normalized = Self::normalize_folder_path(folder_path);
        if normalized.is_empty() {
            return String::new();
        }

        normalized.split('/').last().unwrap_or("").to_string()
    }

    /// 构建文件夹的完整路径
    pub fn build_folder_path(parent_path: &str, folder_name: &str) -> String {
        let parent_normalized = Self::normalize_folder_path(parent_path);
        let folder_name_trimmed = folder_name.trim();

        if parent_normalized.is_empty() {
            folder_name_trimmed.to_string()
        } else {
            format!("{}/{}", parent_normalized, folder_name_trimmed)
        }
    }

    /// 验证文件夹名称是否有效
    pub fn is_valid_folder_name(name: &str) -> bool {
        if name.is_empty() || name.trim().is_empty() {
            return false;
        }

        // 检查是否包含非法字符
        let invalid_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|'];
        if name.chars().any(|c| invalid_chars.contains(&c)) {
            return false;
        }

        // 检查是否是保留名称
        let reserved_names = [".", "..", "CON", "PRN", "AUX", "NUL"];
        if reserved_names.contains(&name.to_uppercase().as_str()) {
            return false;
        }

        true
    }

    /// 清理文件夹名称（移除非法字符）
    pub fn sanitize_folder_name(name: &str) -> String {
        let invalid_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|'];
        name.chars()
            .map(|c| if invalid_chars.contains(&c) { '_' } else { c })
            .collect::<String>()
            .trim()
            .to_string()
    }
}

/// 路径相关的常用操作
pub trait PathExtensions {
    /// 检查路径是否存在且是目录
    fn is_existing_directory(&self) -> bool;
    
    /// 安全地创建目录
    async fn create_dir_safe(&self) -> AppResult<()>;
}

impl PathExtensions for Path {
    fn is_existing_directory(&self) -> bool {
        self.exists() && self.is_dir()
    }

    async fn create_dir_safe(&self) -> AppResult<()> {
        if !self.exists() {
            tokio::fs::create_dir_all(self).await?;
        }
        Ok(())
    }
}

impl PathExtensions for PathBuf {
    fn is_existing_directory(&self) -> bool {
        self.as_path().is_existing_directory()
    }

    async fn create_dir_safe(&self) -> AppResult<()> {
        self.as_path().create_dir_safe().await
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_normalize_folder_path() {
        assert_eq!(PathUtils::normalize_folder_path(""), "");
        assert_eq!(PathUtils::normalize_folder_path("/"), "");
        assert_eq!(PathUtils::normalize_folder_path("folder"), "folder");
        assert_eq!(PathUtils::normalize_folder_path("/folder/"), "folder");
        assert_eq!(PathUtils::normalize_folder_path("parent/child"), "parent/child");
        assert_eq!(PathUtils::normalize_folder_path("//parent//child//"), "parent/child");
    }

    #[test]
    fn test_get_parent_folder_path() {
        assert_eq!(PathUtils::get_parent_folder_path(""), None);
        assert_eq!(PathUtils::get_parent_folder_path("folder"), Some("".to_string()));
        assert_eq!(PathUtils::get_parent_folder_path("parent/child"), Some("parent".to_string()));
        assert_eq!(PathUtils::get_parent_folder_path("a/b/c"), Some("a/b".to_string()));
    }

    #[test]
    fn test_is_subfolder_of() {
        assert!(PathUtils::is_subfolder_of("parent/child", "parent"));
        assert!(PathUtils::is_subfolder_of("a/b/c", "a"));
        assert!(PathUtils::is_subfolder_of("any/path", ""));
        assert!(!PathUtils::is_subfolder_of("parent", "parent/child"));
        assert!(!PathUtils::is_subfolder_of("other", "parent"));
    }

    #[test]
    fn test_is_valid_folder_name() {
        assert!(PathUtils::is_valid_folder_name("valid_name"));
        assert!(PathUtils::is_valid_folder_name("folder-123"));
        assert!(!PathUtils::is_valid_folder_name(""));
        assert!(!PathUtils::is_valid_folder_name("folder/name"));
        assert!(!PathUtils::is_valid_folder_name("folder*name"));
        assert!(!PathUtils::is_valid_folder_name("CON"));
    }
}
