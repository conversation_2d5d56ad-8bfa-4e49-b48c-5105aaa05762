# Lite Papers API 文档

## 概述

Lite Papers 是一个轻量级科研文献管理系统，提供文献存储、搜索、分类和文件管理功能。

**基础信息**
- 基础URL: `http://127.0.0.1:3000`
- 数据格式: JSON
- 字符编码: UTF-8

## 通用响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "success",
  "data": { ... }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "错误描述"
}
```

### 分页响应
```json
{
  "papers": [...],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total": 100,
    "total_pages": 10,
    "has_next": true,
    "has_prev": false
  }
}
```

## 数据模型

### Paper (文献)
```json
{
  "id": "uuid",
  "title": "文献标题",
  "authors": ["作者1", "作者2"],
  "journal": "期刊名称",
  "year": 2023,
  "doi": "10.1000/182",
  "abstract_text": "摘要内容",
  "keywords": ["关键词1", "关键词2"],
  "files": [...],
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-01T00:00:00Z"
}
```

### PaperFile (文献文件)
```json
{
  "name": "文件名.pdf",
  "file_type": "Origin|Image|Note",
  "relative_path": "origin.pdf",
  "size": 1024000,
  "created_at": "2023-01-01T00:00:00Z"
}
```

### Folder (文件夹)
```json
{
  "name": "文件夹名称",
  "path": "/path/to/folder",
  "papers_count": 5,
  "subfolders": [...]
}
```

## API 端点

### 系统健康检查

#### GET /health
检查系统状态

**响应示例:**
```json
{
  "status": "healthy",
  "cache_loaded": true,
  "papers_count": 100,
  "folders_count": 10,
  "uptime_seconds": 3600,
  "timestamp": "2023-01-01T00:00:00Z"
}
```

---

## 文献管理

### GET /api/papers
获取文献列表（支持搜索和筛选）

**查询参数:**
- `q` (string, 可选): 全文搜索关键词（在标题、摘要、关键词中搜索）
- `folder` (string, 可选): 文件夹路径筛选
- `year_start` (integer, 可选): 起始年份
- `year_end` (integer, 可选): 结束年份
- `sort_by` (string, 可选): 排序字段 (created_at|updated_at|title|year|author)，默认created_at
- `sort_order` (string, 可选): 排序顺序 (asc|desc)，默认desc
- `page` (integer, 可选): 页码，默认1
- `page_size` (integer, 可选): 每页数量，默认10，最大100

**使用示例:**
```
# 获取所有文献
GET /api/papers

# 全文搜索
GET /api/papers?q=transformer

# 按文件夹筛选
GET /api/papers?folder=AI Papers

# 按年份范围筛选
GET /api/papers?year_start=2017&year_end=2020

# 组合搜索
GET /api/papers?q=attention&folder=AI Papers&year_start=2017&sort_by=year&sort_order=desc
```

**响应:** 分页的文献列表

### POST /api/papers
创建新文献

**请求体:**
```json
{
  "title": "文献标题",
  "authors": ["作者1", "作者2"],
  "journal": "期刊名称",
  "year": 2023,
  "doi": "10.1000/182",
  "abstract_text": "摘要内容",
  "keywords": ["关键词1", "关键词2"],
}
```

**响应:** 创建的文献对象

### GET /api/papers/{id}
获取指定文献详情

**路径参数:**
- `id` (uuid): 文献ID

**响应:** 文献对象

### PUT /api/papers/{id}
更新文献信息

**路径参数:**
- `id` (uuid): 文献ID

**请求体:** (所有字段可选)
```json
{
  "title": "新标题",
  "authors": ["新作者"],
  "journal": "新期刊",
  "year": 2024,
  "doi": "新DOI",
  "abstract_text": "新摘要",
  "keywords": ["新关键词"]
}
```

**响应:** 更新后的文献对象

### DELETE /api/papers/{id}
删除文献

**路径参数:**
- `id` (uuid): 文献ID

**响应:** 204 No Content



---

## 文件管理

### GET /api/papers/{id}/files
获取文献的文件列表

**路径参数:**
- `id` (uuid): 文献ID

**响应:** 文件列表

### POST /api/papers/{id}/files
上传文件到文献

**路径参数:**
- `id` (uuid): 文献ID

**查询参数:**
- `type` (string, 可选): 文件类型 (image|note|origin)，默认note

**请求:** multipart/form-data
- `file`: 要上传的文件

**响应:** 上传的文件信息

### GET /api/papers/{id}/files/{filename}
下载文献文件

**路径参数:**
- `id` (uuid): 文献ID
- `filename` (string): 文件名

**响应:** 文件流（attachment方式）

### GET /api/papers/{id}/files/preview/{filename}
预览文献文件

**路径参数:**
- `id` (uuid): 文献ID
- `filename` (string): 文件名

**响应:** 文件流（inline方式，适合浏览器预览）

### DELETE /api/papers/{id}/files/{filename}
删除文献文件

**路径参数:**
- `id` (uuid): 文献ID
- `filename` (string): 文件名

**响应:** 204 No Content

---

## 文件夹管理

### GET /api/folders
获取文件夹列表

**查询参数:**
- `path` (string, 可选): 父文件夹路径

**响应:** 文件夹列表

### POST /api/folders
创建文件夹

**请求体:**
```json
{
  "name": "文件夹名称",
  "parent_path": "父文件夹路径"
}
```

**响应:** 创建的文件夹对象

### DELETE /api/folders/{**path}
删除文件夹

**路径参数:**
- `path` (string): 文件夹路径（支持多级路径）

**响应:** 204 No Content

### PUT /api/folders/{**path}
重命名文件夹

**路径参数:**
- `path` (string): 文件夹路径

**请求体:**
```json
{
  "new_name": "新文件夹名称"
}
```

**响应:** 重命名后的文件夹对象

---

## 统计与分析

### GET /api/papers/stats/detailed
获取详细统计信息

**响应:**
```json
{
  "total_papers": 100,
  "total_folders": 10,
  "papers_by_folder": [["AI Papers", 50], ["ML Papers", 30]],
  "papers_by_year": [[2023, 40], [2022, 35]],
  "top_authors": [["张三", 15], ["李四", 12]],
  "top_keywords": [["机器学习", 25], ["深度学习", 20]],
  "recent_papers_count": 5
}
```

### GET /api/papers/folders/tree
获取文件夹树结构

**响应:**
```json
[
  {
    "name": "AI Papers",
    "path": "AI Papers",
    "papers_count": 50,
    "children": []
  }
]
```

---

## 缓存管理

### GET /api/papers/cache/stats
获取缓存统计信息

**响应:**
```json
{
  "papers_count": 100,
  "folders_count": 10,
  "is_loaded": true,
  "last_updated": 3600
}
```

### POST /api/papers/cache/reload
重新加载缓存

**响应:**
```json
{
  "message": "Cache reloaded successfully",
  "papers_count": 100,
  "folders_count": 10
}
```

---

## 数据迁移

### POST /api/papers/migrate
迁移所有文献数据

**响应:**
```json
{
  "message": "Migration completed",
  "migrated_count": 100
}
```

---

## 错误代码

| 状态码 | 说明 |
|--------|------|
| 200 | 成功 |
| 201 | 创建成功 |
| 204 | 删除成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 409 | 资源冲突（如文件夹已存在） |
| 500 | 服务器内部错误 |

## 性能监控

所有API响应都包含性能头信息：
- `X-Response-Time`: 请求处理时间（毫秒）

## 支持的文件格式

**图片格式:** jpg, jpeg, png, gif, bmp, webp, svg
**文档格式:** pdf, txt, md, json

## 使用示例

### 创建文献并上传文件
```bash
# 1. 创建文献
curl -X POST http://127.0.0.1:3000/api/papers \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Attention Is All You Need",
    "authors": ["Vaswani", "Shazeer", "Parmar"],
    "journal": "NIPS",
    "year": 2017,
    "keywords": ["transformer", "attention", "neural networks"],
    "folder_path": "AI Papers"
  }'

# 2. 上传原始PDF文件
curl -X POST http://127.0.0.1:3000/api/papers/{paper_id}/files?type=origin \
  -F "file=@paper.pdf"

# 3. 上传笔记文件
curl -X POST http://127.0.0.1:3000/api/papers/{paper_id}/files?type=note \
  -F "file=@notes.txt"
```

### 搜索文献
```bash
# 全文搜索
curl "http://127.0.0.1:3000/api/papers/search?q=transformer"

# 复合搜索
curl "http://127.0.0.1:3000/api/papers/search?q=attention&year_start=2017&author=vaswani&sort_by=year&sort_order=desc"

# 按文件夹搜索
curl "http://127.0.0.1:3000/api/papers/search?folder=AI%20Papers&page=1&page_size=20"
```

### 文件夹操作
```bash
# 创建文件夹
curl -X POST http://127.0.0.1:3000/api/folders \
  -H "Content-Type: application/json" \
  -d '{"name": "Deep Learning", "parent_path": "AI Papers"}'

# 获取文件夹树
curl "http://127.0.0.1:3000/api/papers/folders/tree"
```

## 最佳实践

### 1. 分页查询
- 使用合适的页面大小（建议10-50）
- 大数据量查询时使用搜索过滤条件
- 利用 `has_next` 和 `has_prev` 实现分页导航

### 2. 文件上传
- 支持的文件类型：`origin`（原始论文）、`note`（笔记）、`image`（图片）
- 文件大小建议控制在合理范围内
- 使用有意义的文件名

### 3. 搜索优化
- 使用全文搜索 `q` 参数进行快速查找
- 结合多个过滤条件精确定位
- 利用排序功能获得更好的结果顺序

### 4. 错误处理
- 检查HTTP状态码
- 解析错误响应中的详细信息
- 实现适当的重试机制

### 5. 性能优化
- 监控 `X-Response-Time` 头信息
- 使用缓存统计接口了解系统状态
- 必要时使用缓存重载功能

## 配置说明

系统配置通过 `config.toml` 文件管理，支持以下配置项：
- 服务器端口和地址
- 分页参数限制
- 文件格式支持
- 统计显示数量
- 搜索参数配置

## 开发者注意事项

1. **UUID格式**: 所有文献ID使用标准UUID格式
2. **时间格式**: 使用ISO 8601格式 (RFC 3339)
3. **文件路径**: 使用相对路径，支持多级目录
4. **字符编码**: 全部使用UTF-8编码
5. **并发安全**: API支持并发访问，内部使用读写锁保护数据一致性
