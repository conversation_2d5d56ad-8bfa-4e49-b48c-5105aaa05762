use crate::models::paper::{Paper, PaginatedPapers, PaginationInfo};
use crate::error::AppResult;
use std::collections::HashMap;
use std::sync::Arc;
use std::sync::atomic::{AtomicBool, Ordering};
use tokio::sync::RwLock;
use uuid::Uuid;
use std::path::{Path, PathBuf};
use std::collections::VecDeque;
use tokio::fs;
use std::time::Instant;
use serde::Serialize;

/// 全量内存缓存管理器
#[derive(Clone, Debug)]
pub struct PaperCache {
    /// 主数据存储：Paper ID -> Paper对象
    papers: Arc<RwLock<HashMap<Uuid, Paper>>>,

    /// Paper物理路径映射：Paper ID -> 物理路径
    paper_paths: Arc<RwLock<HashMap<Uuid, PathBuf>>>,

    /// 文件夹索引：文件夹路径 -> Paper ID列表
    folder_index: Arc<RwLock<HashMap<String, Vec<Uuid>>>>,

    /// 标题索引：标题关键词 -> Paper ID列表
    title_index: Arc<RwLock<HashMap<String, Vec<Uuid>>>>,

    /// 关键词索引：关键词 -> Paper ID列表
    keyword_index: Arc<RwLock<HashMap<String, Vec<Uuid>>>>,

    /// 摘要索引：摘要关键词 -> Paper ID列表
    abstract_index: Arc<RwLock<HashMap<String, Vec<Uuid>>>>,

    /// 年份索引：年份 -> Paper ID列表
    year_index: Arc<RwLock<HashMap<u32, Vec<Uuid>>>>,

    /// 缓存加载状态
    loaded: Arc<AtomicBool>,

    /// 最后更新时间
    last_updated: Arc<RwLock<Instant>>,
}

impl PaperCache {
    /// 创建新的缓存实例
    pub fn new() -> Self {
        Self {
            papers: Arc::new(RwLock::new(HashMap::new())),
            paper_paths: Arc::new(RwLock::new(HashMap::new())),
            folder_index: Arc::new(RwLock::new(HashMap::new())),
            title_index: Arc::new(RwLock::new(HashMap::new())),
            keyword_index: Arc::new(RwLock::new(HashMap::new())),
            abstract_index: Arc::new(RwLock::new(HashMap::new())),
            year_index: Arc::new(RwLock::new(HashMap::new())),
            loaded: Arc::new(AtomicBool::new(false)),
            last_updated: Arc::new(RwLock::new(Instant::now())),
        }
    }

    /// 初始化缓存，加载所有Paper数据
    pub async fn initialize(&self) -> AppResult<()> {
        if self.loaded.load(Ordering::Acquire) {
            return Ok(());
        }

        tracing::info!("开始加载Paper缓存...");
        let start_time = Instant::now();

        self.load_all_papers().await?;
        self.build_all_indexes().await?;

        self.loaded.store(true, Ordering::Release);
        *self.last_updated.write().await = Instant::now();

        let papers_count = self.papers.read().await.len();
        tracing::info!("缓存加载完成！共加载 {} 篇文献，耗时 {:?}",
                papers_count, start_time.elapsed());
        
        Ok(())
    }

    /// 加载所有Paper数据
    async fn load_all_papers(&self) -> AppResult<()> {
        let config = crate::config::get_config();
        let papers_dir = PathBuf::from(&config.storage.papers_directory);
        let mut papers_map = HashMap::new();
        let mut paths_map = HashMap::new();

        self.scan_papers_recursive(&papers_dir, &mut papers_map, &mut paths_map).await?;

        *self.papers.write().await = papers_map;
        *self.paper_paths.write().await = paths_map;
        Ok(())
    }

    /// 递归扫描papers目录，加载所有meta.json文件
    async fn scan_papers_recursive(&self, dir: &Path, papers_map: &mut HashMap<Uuid, Paper>, paths_map: &mut HashMap<Uuid, PathBuf>) -> AppResult<()> {
        if !dir.exists() {
            return Ok(());
        }

        let mut dirs_to_process = VecDeque::new();
        dirs_to_process.push_back(dir.to_path_buf());

        while let Some(current_dir) = dirs_to_process.pop_front() {
            let mut entries = fs::read_dir(&current_dir).await?;

            while let Some(entry) = entries.next_entry().await? {
                let path = entry.path();

                if path.is_dir() {
                    let name = path.file_name()
                        .and_then(|n| n.to_str())
                        .unwrap_or("");

                    // 如果是UUID命名的文件夹，尝试读取文献
                    if let Ok(paper_id) = Uuid::parse_str(name) {
                        let meta_file = path.join("meta.json");
                        if meta_file.exists() {
                            match self.load_paper_from_file(&meta_file).await {
                                Ok(paper) => {
                                    papers_map.insert(paper_id, paper);
                                    paths_map.insert(paper_id, path.clone());
                                }
                                Err(e) => {
                                    tracing::error!("加载文献 {} 失败: {}", paper_id, e);
                                }
                            }
                        }
                    } else {
                        // 添加到待处理队列
                        dirs_to_process.push_back(path);
                    }
                }
            }
        }

        Ok(())
    }

    /// 从文件加载单个Paper
    async fn load_paper_from_file(&self, meta_file: &Path) -> AppResult<Paper> {
        let meta_content = fs::read_to_string(meta_file).await?;
        let paper: Paper = serde_json::from_str(&meta_content)?;
        Ok(paper)
    }

    /// 构建所有索引
    async fn build_all_indexes(&self) -> AppResult<()> {
        let papers = self.papers.read().await;
        let paper_paths = self.paper_paths.read().await;

        let mut folder_index = HashMap::new();
        let mut title_index = HashMap::new();
        let mut keyword_index = HashMap::new();
        let mut abstract_index = HashMap::new();
        let mut year_index = HashMap::new();

        for (paper_id, paper) in papers.iter() {
            // 文件夹索引 - 使用物理路径推导虚拟文件夹路径
            let folder_path = if let Some(physical_path) = paper_paths.get(paper_id) {
                paper.get_folder_path(physical_path)
            } else {
                // 如果找不到物理路径，使用缓存版本（返回空字符串）
                paper.get_folder_path_cached()
            };

            folder_index
                .entry(folder_path)
                .or_insert_with(Vec::new)
                .push(*paper_id);

            // 标题索引
            Self::add_text_to_index(&paper.title, *paper_id, &mut title_index);

            // 关键词索引
            for keyword in &paper.keywords {
                Self::add_text_to_index(keyword, *paper_id, &mut keyword_index);
            }

            // 摘要索引
            if let Some(ref abstract_text) = paper.abstract_text {
                Self::add_text_to_index(abstract_text, *paper_id, &mut abstract_index);
            }

            // 年份索引
            if let Some(year) = paper.year {
                year_index
                    .entry(year)
                    .or_insert_with(Vec::new)
                    .push(*paper_id);
            }
        }

        // 释放读锁
        drop(papers);
        drop(paper_paths);

        // 更新所有索引
        *self.folder_index.write().await = folder_index;
        *self.title_index.write().await = title_index;
        *self.keyword_index.write().await = keyword_index;
        *self.abstract_index.write().await = abstract_index;
        *self.year_index.write().await = year_index;

        Ok(())
    }

    /// 将文本分词并添加到索引中
    fn add_text_to_index(text: &str, paper_id: Uuid, index: &mut HashMap<String, Vec<Uuid>>) {
        // 简单的分词策略：按空格、标点符号分割，转为小写
        let words: Vec<String> = text
            .to_lowercase()
            .split_whitespace()
            .map(|word| {
                // 移除标点符号
                word.chars()
                    .filter(|c| c.is_alphanumeric() || c.is_ascii_alphabetic())
                    .collect::<String>()
            })
            .filter(|word| word.len() > crate::config::get_config().search.min_word_length) // 过滤太短的词
            .collect();

        for word in words {
            if !word.is_empty() {
                index
                    .entry(word)
                    .or_insert_with(Vec::new)
                    .push(paper_id);
            }
        }
    }

    /// 检查缓存是否已加载
    pub fn is_loaded(&self) -> bool {
        self.loaded.load(Ordering::Acquire)
    }

    /// 获取Paper的虚拟文件夹路径
    async fn get_paper_folder_path(&self, paper_id: &Uuid, paper: &Paper) -> String {
        if let Some(physical_path) = self.paper_paths.read().await.get(paper_id) {
            paper.get_folder_path(physical_path)
        } else {
            paper.get_folder_path_cached()
        }
    }

    /// 获取缓存统计信息
    pub async fn get_stats(&self) -> CacheStats {
        let papers_count = self.papers.read().await.len();
        let folders_count = self.folder_index.read().await.len();
        let last_updated = *self.last_updated.read().await;

        CacheStats {
            papers_count,
            folders_count,
            last_updated,
            is_loaded: self.is_loaded(),
        }
    }

    // ==================== 查询功能 ====================

    /// 根据ID获取Paper
    pub async fn get_paper(&self, id: &Uuid) -> Option<Paper> {
        self.papers.read().await.get(id).cloned()
    }

    /// 根据ID获取Paper的物理路径
    pub async fn get_paper_path(&self, id: &Uuid) -> Option<PathBuf> {
        self.paper_paths.read().await.get(id).cloned()
    }

    /// 根据ID获取Paper和其物理路径（避免不必要的克隆）
    pub async fn get_paper_with_path(&self, id: &Uuid) -> Option<(Paper, PathBuf)> {
        let papers = self.papers.read().await;
        let paths = self.paper_paths.read().await;

        match (papers.get(id), paths.get(id)) {
            (Some(paper), Some(path)) => Some((paper.clone(), path.clone())),
            _ => None,
        }
    }





    /// 分页查询Paper（使用统一搜索方法）
    pub async fn get_papers_paginated(
        &self,
        folder_path: Option<&str>,
        page: u32,
        page_size: u32,
    ) -> PaginatedPapers {
        let mut criteria = SearchCriteria::default();
        criteria.folder_path = folder_path.map(|s| s.to_string());
        // 默认按创建时间降序排序
        criteria.sort_by = SortBy::CreatedAt;
        criteria.order = SortOrder::Desc;

        let papers = self.search(&criteria).await;
        self.paginate_papers(papers, page, page_size)
    }

    /// 搜索结果分页
    pub async fn search_paginated(
        &self,
        criteria: &SearchCriteria,
        page: u32,
        page_size: u32,
    ) -> PaginatedPapers {
        let papers = self.search(criteria).await;
        self.paginate_papers(papers, page, page_size)
    }



    /// 通用分页方法
    fn paginate_papers(
        &self,
        papers: Vec<Paper>,
        page: u32,
        page_size: u32,
    ) -> PaginatedPapers {
        let total = papers.len() as u32;
        let start_index = ((page - 1) * page_size) as usize;
        let end_index = (start_index + page_size as usize).min(papers.len());

        let page_papers = if start_index < papers.len() {
            papers[start_index..end_index].to_vec()
        } else {
            Vec::new()
        };

        let pagination = PaginationInfo::new(page, page_size, total);

        PaginatedPapers {
            papers: page_papers,
            pagination,
        }
    }

    /// 统计指定文件夹中的Paper数量
    pub async fn count_papers_by_folder(&self, folder_path: &str) -> usize {
        self.folder_index
            .read()
            .await
            .get(folder_path)
            .map(|ids| ids.len())
            .unwrap_or(0)
    }

    /// 检查文件夹是否包含文献（包括子文件夹）
    pub async fn has_papers_in_folder(&self, folder_path: &str) -> (bool, usize) {
        let folder_index = self.folder_index.read().await;
        let mut total_papers = 0;

        for (path, paper_ids) in folder_index.iter() {
            // 检查是否是该文件夹或其子文件夹
            if path == folder_path || path.starts_with(&format!("{}/", folder_path)) {
                total_papers += paper_ids.len();
            }
        }

        (total_papers > 0, total_papers)
    }

    // ==================== 搜索功能 ====================

    /// 在指定索引中搜索关键词
    async fn search_in_index(
        &self,
        keyword: &str,
        index: &Arc<RwLock<HashMap<String, Vec<Uuid>>>>
    ) -> Vec<Uuid> {
        let index_guard = index.read().await;
        let keyword_lower = keyword.to_lowercase();
        let mut result_ids = Vec::new();

        for (word, ids) in index_guard.iter() {
            if word.contains(&keyword_lower) {
                result_ids.extend(ids);
            }
        }

        result_ids.sort();
        result_ids.dedup();
        result_ids
    }

    /// 根据年份范围搜索Paper ID
    async fn search_by_year_range(&self, start_year: u32, end_year: u32) -> Vec<Uuid> {
        let year_index = self.year_index.read().await;
        let mut result_ids = Vec::new();

        for year in start_year..=end_year {
            if let Some(ids) = year_index.get(&year) {
                result_ids.extend(ids);
            }
        }

        result_ids.sort();
        result_ids.dedup();
        result_ids
    }

    /// 精简搜索方法：支持全文搜索、文件夹筛选、年份筛选
    pub async fn search(&self, criteria: &SearchCriteria) -> Vec<Paper> {
        let papers = self.papers.read().await;
        let mut result_ids: Option<Vec<Uuid>> = None;

        // 1. 全文搜索（如果提供了查询词）
        if let Some(ref query) = criteria.query {
            let mut all_ids = Vec::new();

            // 在标题、摘要、关键词中搜索
            all_ids.extend(self.search_in_index(query, &self.title_index).await);
            all_ids.extend(self.search_in_index(query, &self.abstract_index).await);
            all_ids.extend(self.search_in_index(query, &self.keyword_index).await);

            // 去重
            all_ids.sort();
            all_ids.dedup();

            result_ids = Some(all_ids);
        }

        // 2. 文件夹筛选
        if let Some(ref folder_path) = criteria.folder_path {
            let folder_index = self.folder_index.read().await;
            if let Some(folder_ids) = folder_index.get(folder_path) {
                result_ids = Some(self.intersect_ids(result_ids, folder_ids.clone()));
            } else {
                return Vec::new(); // 文件夹不存在，直接返回空结果
            }
        }

        // 3. 年份范围筛选
        if let Some((start_year, end_year)) = criteria.year_range {
            let year_ids = self.search_by_year_range(start_year, end_year).await;
            result_ids = Some(self.intersect_ids(result_ids, year_ids));
        }

        // 如果没有任何搜索条件，返回所有Paper
        let final_ids = result_ids.unwrap_or_else(|| papers.keys().cloned().collect());

        // 获取Paper对象（减少克隆）
        let mut results = Vec::with_capacity(final_ids.len());
        for id in &final_ids {
            if let Some(paper) = papers.get(id) {
                results.push(paper.clone());
            }
        }

        // 排序
        drop(papers); // 释放读锁
        self.sort_papers(results, &criteria.sort_by, &criteria.order)
    }

    /// 计算ID列表的交集（优化内存使用）
    fn intersect_ids(&self, list1: Option<Vec<Uuid>>, list2: Vec<Uuid>) -> Vec<Uuid> {
        match list1 {
            Some(mut ids1) => {
                use std::collections::HashSet;
                let set2: HashSet<Uuid> = list2.into_iter().collect();
                ids1.retain(|id| set2.contains(id));
                ids1
            }
            None => list2,
        }
    }



    /// 对Paper列表进行排序
    fn sort_papers(&self, mut papers: Vec<Paper>, sort_by: &SortBy, order: &SortOrder) -> Vec<Paper> {
        match sort_by {
            SortBy::CreatedAt => {
                papers.sort_by(|a, b| a.created_at.cmp(&b.created_at));
            }
            SortBy::UpdatedAt => {
                papers.sort_by(|a, b| a.updated_at.cmp(&b.updated_at));
            }
            SortBy::Title => {
                papers.sort_by(|a, b| a.title.cmp(&b.title));
            }
            SortBy::Year => {
                papers.sort_by(|a, b| a.year.cmp(&b.year));
            }
            SortBy::Author => {
                papers.sort_by(|a, b| {
                    let a_author = a.authors.first().map(|s| s.as_str()).unwrap_or("");
                    let b_author = b.authors.first().map(|s| s.as_str()).unwrap_or("");
                    a_author.cmp(b_author)
                });
            }
        }

        match order {
            SortOrder::Asc => papers,
            SortOrder::Desc => {
                papers.reverse();
                papers
            }
        }
    }



    // ==================== 统计分析功能 ====================

    /// 获取详细统计信息
    pub async fn get_detailed_stats(&self) -> DetailedStats {
        let papers = self.papers.read().await;
        let folder_index = self.folder_index.read().await;
        let year_index = self.year_index.read().await;
        let keyword_index = self.keyword_index.read().await;

        // 按文件夹统计
        let papers_by_folder: Vec<(String, usize)> = folder_index
            .iter()
            .map(|(folder, ids)| (folder.clone(), ids.len()))
            .collect();

        // 按年份统计
        let mut papers_by_year: Vec<(u32, usize)> = year_index
            .iter()
            .map(|(year, ids)| (*year, ids.len()))
            .collect();
        papers_by_year.sort_by(|a, b| b.0.cmp(&a.0)); // 按年份倒序

        // 获取配置
        let config = crate::config::get_config();

        // 热门作者（直接从Paper数据计算）
        let mut author_counts = std::collections::HashMap::new();
        for paper in papers.values() {
            for author in &paper.authors {
                *author_counts.entry(author.clone()).or_insert(0) += 1;
            }
        }
        let mut top_authors: Vec<(String, usize)> = author_counts.into_iter().collect();
        top_authors.sort_by(|a, b| b.1.cmp(&a.1));
        let top_authors = top_authors.into_iter().take(config.stats.top_authors_limit).collect();

        // 热门关键词
        let mut keyword_counts: Vec<(String, usize)> = keyword_index
            .iter()
            .map(|(keyword, ids)| (keyword.clone(), ids.len()))
            .collect();
        keyword_counts.sort_by(|a, b| b.1.cmp(&a.1));
        let top_keywords = keyword_counts.into_iter().take(config.stats.top_keywords_limit).collect();

        // 最近添加的文献（优化内存使用）
        let mut recent_papers: Vec<&Paper> = papers.values().collect();
        recent_papers.sort_by(|a, b| b.created_at.cmp(&a.created_at));
        let recent_papers: Vec<Paper> = recent_papers
            .into_iter()
            .take(config.stats.recent_papers_limit)
            .cloned()
            .collect();

        DetailedStats {
            total_papers: papers.len(),
            total_folders: folder_index.len(),
            papers_by_folder,
            papers_by_year,
            top_authors,
            top_keywords,
            recent_papers,
        }
    }

    /// 获取文件夹树结构（带文献数量）
    pub async fn get_folder_tree(&self) -> Vec<FolderNode> {
        let config = crate::config::get_config();
        let folder_index = self.folder_index.read().await;

        // 重新扫描文件系统以获取最新的文件夹结构
        let all_folders = self.scan_all_folders().await;

        // 使用简化的树结构
        let mut root_tree: std::collections::HashMap<String, TreeNodeData> = std::collections::HashMap::new();

        // 添加所有子文件夹（跳过根目录，单独处理）
        for folder_path in all_folders.iter() {
            if !folder_path.is_empty() { // 跳过根目录
                let paper_count = folder_index.get(folder_path).map(|ids| ids.len()).unwrap_or(0);
                let parts: Vec<&str> = folder_path.split('/').filter(|s| !s.is_empty()).collect();
                self.insert_folder_path(&mut root_tree, &parts, paper_count);
            }
        }

        let mut nodes = self.tree_to_folder_nodes(&root_tree, "");

        // 添加根目录节点（始终在最前面）
        let root_papers_count = folder_index.get("").map(|ids| ids.len()).unwrap_or(0);
        let root_node = FolderNode {
            name: format!("根目录 ({})", config.storage.papers_directory),
            path: String::new(), // 空字符串表示根目录
            papers_count: root_papers_count,
            children: Vec::new(),
        };

        // 根目录始终在最前面
        nodes.insert(0, root_node);
        nodes
    }

    /// 扫描所有文件夹（包括根目录和空文件夹）
    async fn scan_all_folders(&self) -> Vec<String> {
        let mut folders = Vec::new();
        let config = crate::config::get_config();
        let papers_dir = std::path::PathBuf::from(&config.storage.papers_directory);

        // 添加根目录（空字符串表示）
        folders.push(String::new());

        if let Err(_) = self.scan_folders_recursive(&papers_dir, "", &mut folders).await {
            // 如果扫描失败，至少返回根目录
            return vec![String::new()];
        }

        // 去重（可能重复添加了根目录）
        folders.sort();
        folders.dedup();

        folders
    }

    /// 迭代式扫描文件夹（替代复杂的异步递归）
    async fn scan_folders_recursive(
        &self,
        root_dir: &std::path::Path,
        root_relative_path: &str,
        folders: &mut Vec<String>,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        use tokio::fs;
        use crate::utils::file_utils::is_uuid_folder;
        use std::collections::VecDeque;

        if !root_dir.exists() {
            return Ok(());
        }

        // 使用队列实现迭代式扫描，避免复杂的异步递归
        let mut queue = VecDeque::new();
        queue.push_back((root_dir.to_path_buf(), root_relative_path.to_string()));

        while let Some((current_dir, relative_path)) = queue.pop_front() {
            let mut entries = fs::read_dir(&current_dir).await?;

            while let Some(entry) = entries.next_entry().await? {
                let path = entry.path();

                if path.is_dir() {
                    let name = path.file_name()
                        .and_then(|n| n.to_str())
                        .unwrap_or("");

                    // 跳过UUID命名的文献文件夹
                    if !is_uuid_folder(name) {
                        let folder_path = if relative_path.is_empty() {
                            name.to_string()
                        } else {
                            format!("{}/{}", relative_path, name)
                        };

                        folders.push(folder_path.clone());

                        // 添加到队列中继续扫描
                        queue.push_back((path, folder_path));
                    }
                }
            }
        }

        Ok(())
    }

    /// 将文件夹路径插入到树结构中
    fn insert_folder_path(
        &self,
        tree: &mut std::collections::HashMap<String, TreeNodeData>,
        parts: &[&str],
        paper_count: usize,
    ) {
        if parts.is_empty() {
            return;
        }

        let current = parts[0].to_string();
        let remaining = &parts[1..];

        let node = tree.entry(current).or_insert(TreeNodeData {
            papers_count: 0,
            children: std::collections::HashMap::new(),
        });

        if remaining.is_empty() {
            // 叶子节点，设置文献数量
            node.papers_count = paper_count;
        } else {
            // 中间节点，递归处理子路径
            self.insert_folder_path(&mut node.children, remaining, paper_count);
        }
    }

    /// 将树结构转换为FolderNode列表
    fn tree_to_folder_nodes(
        &self,
        tree: &std::collections::HashMap<String, TreeNodeData>,
        parent_path: &str,
    ) -> Vec<FolderNode> {
        let mut nodes: Vec<FolderNode> = tree
            .iter()
            .map(|(name, node_data)| {
                let current_path = if parent_path.is_empty() {
                    name.clone()
                } else {
                    format!("{}/{}", parent_path, name)
                };

                let children = self.tree_to_folder_nodes(&node_data.children, &current_path);

                FolderNode {
                    name: name.clone(),
                    path: current_path,
                    papers_count: node_data.papers_count,
                    children,
                }
            })
            .collect();

        // 按名称排序
        nodes.sort_by(|a, b| a.name.cmp(&b.name));
        nodes
    }

    // ==================== 写时更新功能 ====================

    /// 添加新Paper到缓存（需要提供物理路径）
    pub async fn add_paper_with_path(&self, paper: Paper, physical_path: PathBuf) {
        let paper_id = paper.id;

        // 更新主缓存
        self.papers.write().await.insert(paper_id, paper.clone());

        // 更新物理路径映射
        self.paper_paths.write().await.insert(paper_id, physical_path.clone());

        // 更新所有索引
        // 文件夹索引
        let folder_path = paper.get_folder_path(&physical_path);
        self.folder_index
            .write()
            .await
            .entry(folder_path)
            .or_insert_with(Vec::new)
            .push(paper_id);

        // 标题索引
        self.add_text_to_index_runtime(&paper.title, paper_id, &self.title_index).await;

        // 关键词索引
        for keyword in &paper.keywords {
            self.add_text_to_index_runtime(keyword, paper_id, &self.keyword_index).await;
        }

        // 摘要索引
        if let Some(ref abstract_text) = paper.abstract_text {
            self.add_text_to_index_runtime(abstract_text, paper_id, &self.abstract_index).await;
        }

        // 年份索引
        if let Some(year) = paper.year {
            self.year_index
                .write()
                .await
                .entry(year)
                .or_insert_with(Vec::new)
                .push(paper_id);
        }

        // 更新时间戳
        *self.last_updated.write().await = Instant::now();
    }



    /// 更新缓存中的Paper（增量索引更新）
    pub async fn update_paper(&self, paper: Paper) {
        let paper_id = paper.id;

        // 获取旧的Paper数据用于清理索引
        let old_paper = self.papers.read().await.get(&paper_id).cloned();

        // 更新主缓存
        self.papers.write().await.insert(paper_id, paper.clone());

        // 增量更新索引
        if let Some(old_paper) = old_paper {
            self.update_paper_indexes_incremental(&old_paper, &paper).await;
        } else {
            // 如果是新Paper，直接添加到索引
            if let Some(physical_path) = self.paper_paths.read().await.get(&paper_id) {
                self.add_paper_to_indexes(&paper, physical_path).await;
            }
        }

        // 更新时间戳
        *self.last_updated.write().await = Instant::now();
    }

    /// 从缓存中删除Paper
    pub async fn remove_paper(&self, paper_id: &Uuid) -> Option<Paper> {
        // 从主缓存中移除
        let removed_paper = self.papers.write().await.remove(paper_id);

        if let Some(ref paper) = removed_paper {
            // 从文件夹索引中移除
            let folder_path = self.get_paper_folder_path(paper_id, paper).await;
            if let Some(ids) = self.folder_index.write().await.get_mut(&folder_path) {
                ids.retain(|&id| id != *paper_id);
                if ids.is_empty() {
                    self.folder_index.write().await.remove(&folder_path);
                }
            }
        }

        // 从物理路径映射中移除
        self.paper_paths.write().await.remove(paper_id);

        // 更新时间戳
        *self.last_updated.write().await = Instant::now();

        removed_paper
    }

    /// 清空所有缓存
    pub async fn clear(&self) {
        self.papers.write().await.clear();
        self.paper_paths.write().await.clear();
        self.folder_index.write().await.clear();
        self.title_index.write().await.clear();
        self.keyword_index.write().await.clear();
        self.abstract_index.write().await.clear();
        self.year_index.write().await.clear();
        self.loaded.store(false, Ordering::Release);
        *self.last_updated.write().await = Instant::now();
    }

    /// 重新加载缓存
    pub async fn reload(&self) -> AppResult<()> {
        self.clear().await;
        self.initialize().await
    }

    /// 更新缓存时间戳（用于文件夹操作等不直接影响Paper数据的操作）
    pub async fn touch(&self) {
        *self.last_updated.write().await = Instant::now();
    }

    /// 更新文件夹路径（用于文件夹重命名）
    /// 在虚拟文件夹路径设计下，这个方法主要用于重建索引
    pub async fn update_folder_path(&self, _old_path: &str, _new_path: &str) -> AppResult<()> {
        // 在虚拟文件夹路径设计下，文件夹路径是从物理路径推导的
        // 当文件夹重命名时，物理路径已经改变，我们只需要重建索引即可

        tracing::info!("文件夹重命名后重建索引...");

        // 重建文件夹索引
        if let Err(e) = self.rebuild_folder_index().await {
            tracing::error!("重建文件夹索引失败: {}", e);
            return Err(e);
        }

        // 更新时间戳
        *self.last_updated.write().await = Instant::now();

        Ok(())
    }

    /// 重建文件夹索引
    async fn rebuild_folder_index(&self) -> AppResult<()> {
        let papers = self.papers.read().await;
        let paper_paths = self.paper_paths.read().await;
        let mut new_folder_index = std::collections::HashMap::new();

        for (paper_id, paper) in papers.iter() {
            let folder_path = if let Some(physical_path) = paper_paths.get(paper_id) {
                paper.get_folder_path(physical_path)
            } else {
                paper.get_folder_path_cached()
            };

            new_folder_index
                .entry(folder_path)
                .or_insert_with(Vec::new)
                .push(*paper_id);
        }

        drop(papers);
        drop(paper_paths);

        *self.folder_index.write().await = new_folder_index;
        Ok(())
    }



    /// 删除文件夹及其下的所有文献（用于文件夹删除）
    pub async fn remove_folder_and_papers(&self, folder_path: &str) -> AppResult<Vec<Uuid>> {
        let mut papers = self.papers.write().await;
        let mut removed_paper_ids = Vec::new();

        // 获取物理路径映射
        let paper_paths = self.paper_paths.read().await;

        // 找到所有需要删除的文献
        let papers_to_remove: Vec<Uuid> = papers
            .iter()
            .filter(|(paper_id, paper)| {
                let current_folder_path = if let Some(physical_path) = paper_paths.get(paper_id) {
                    paper.get_folder_path(physical_path)
                } else {
                    paper.get_folder_path_cached()
                };
                current_folder_path == folder_path || current_folder_path.starts_with(&format!("{}/", folder_path))
            })
            .map(|(id, _)| *id)
            .collect();

        drop(paper_paths); // 释放读锁

        // 从缓存中移除这些文献
        for paper_id in papers_to_remove {
            if papers.remove(&paper_id).is_some() {
                removed_paper_ids.push(paper_id);
            }
        }

        drop(papers); // 释放写锁

        // 从物理路径映射中移除这些文献
        let mut paths = self.paper_paths.write().await;
        for paper_id in &removed_paper_ids {
            paths.remove(paper_id);
        }
        drop(paths);

        // 重建文件夹索引
        if !removed_paper_ids.is_empty() {
            tracing::info!("删除了 {} 篇文献", removed_paper_ids.len());
            if let Err(e) = self.rebuild_folder_index().await {
                tracing::error!("重建文件夹索引失败: {}", e);
            }
        }

        // 更新时间戳
        *self.last_updated.write().await = Instant::now();

        Ok(removed_paper_ids)
    }

    // ==================== 索引维护辅助方法 ====================

    /// 运行时添加文本到索引
    async fn add_text_to_index_runtime(
        &self,
        text: &str,
        paper_id: Uuid,
        index: &Arc<RwLock<HashMap<String, Vec<Uuid>>>>
    ) {
        let words: Vec<String> = text
            .to_lowercase()
            .split_whitespace()
            .map(|word| {
                word.chars()
                    .filter(|c| c.is_alphanumeric() || c.is_ascii_alphabetic())
                    .collect::<String>()
            })
            .filter(|word| word.len() > 2)
            .collect();

        let mut index_guard = index.write().await;
        for word in words {
            if !word.is_empty() {
                index_guard
                    .entry(word)
                    .or_insert_with(Vec::new)
                    .push(paper_id);
            }
        }
    }

    /// 从索引中移除文本
    async fn remove_text_from_index_runtime(
        &self,
        text: &str,
        paper_id: Uuid,
        index: &Arc<RwLock<HashMap<String, Vec<Uuid>>>>
    ) {
        let words: Vec<String> = text
            .to_lowercase()
            .split_whitespace()
            .map(|word| {
                word.chars()
                    .filter(|c| c.is_alphanumeric() || c.is_ascii_alphabetic())
                    .collect::<String>()
            })
            .filter(|word| word.len() > 2)
            .collect();

        let mut index_guard = index.write().await;
        for word in words {
            if !word.is_empty() {
                if let Some(ids) = index_guard.get_mut(&word) {
                    ids.retain(|&id| id != paper_id);
                    if ids.is_empty() {
                        index_guard.remove(&word);
                    }
                }
            }
        }
    }

    /// 增量更新Paper索引
    async fn update_paper_indexes_incremental(&self, old_paper: &Paper, new_paper: &Paper) {
        let paper_id = new_paper.id;

        // 更新文件夹索引（如果文件夹路径改变了）
        let old_folder_path = self.get_paper_folder_path(&paper_id, old_paper).await;
        let new_folder_path = self.get_paper_folder_path(&paper_id, new_paper).await;

        if old_folder_path != new_folder_path {
            // 从旧文件夹索引中移除
            if let Some(ids) = self.folder_index.write().await.get_mut(&old_folder_path) {
                ids.retain(|&id| id != paper_id);
                if ids.is_empty() {
                    self.folder_index.write().await.remove(&old_folder_path);
                }
            }

            // 添加到新文件夹索引
            self.folder_index
                .write()
                .await
                .entry(new_folder_path)
                .or_insert_with(Vec::new)
                .push(paper_id);
        }

        // 更新标题索引
        if old_paper.title != new_paper.title {
            self.remove_text_from_index_runtime(&old_paper.title, paper_id, &self.title_index).await;
            self.add_text_to_index_runtime(&new_paper.title, paper_id, &self.title_index).await;
        }

        // 更新关键词索引
        if old_paper.keywords != new_paper.keywords {
            for keyword in &old_paper.keywords {
                self.remove_text_from_index_runtime(keyword, paper_id, &self.keyword_index).await;
            }
            for keyword in &new_paper.keywords {
                self.add_text_to_index_runtime(keyword, paper_id, &self.keyword_index).await;
            }
        }

        // 更新摘要索引
        if old_paper.abstract_text != new_paper.abstract_text {
            if let Some(ref old_abstract) = old_paper.abstract_text {
                self.remove_text_from_index_runtime(old_abstract, paper_id, &self.abstract_index).await;
            }
            if let Some(ref new_abstract) = new_paper.abstract_text {
                self.add_text_to_index_runtime(new_abstract, paper_id, &self.abstract_index).await;
            }
        }

        // 更新年份索引
        if old_paper.year != new_paper.year {
            if let Some(old_year) = old_paper.year {
                if let Some(ids) = self.year_index.write().await.get_mut(&old_year) {
                    ids.retain(|&id| id != paper_id);
                    if ids.is_empty() {
                        self.year_index.write().await.remove(&old_year);
                    }
                }
            }
            if let Some(new_year) = new_paper.year {
                self.year_index
                    .write()
                    .await
                    .entry(new_year)
                    .or_insert_with(Vec::new)
                    .push(paper_id);
            }
        }
    }

    /// 将Paper添加到所有索引
    async fn add_paper_to_indexes(&self, paper: &Paper, physical_path: &PathBuf) {
        let paper_id = paper.id;

        // 文件夹索引
        let folder_path = paper.get_folder_path(physical_path);
        self.folder_index
            .write()
            .await
            .entry(folder_path)
            .or_insert_with(Vec::new)
            .push(paper_id);

        // 标题索引
        self.add_text_to_index_runtime(&paper.title, paper_id, &self.title_index).await;

        // 关键词索引
        for keyword in &paper.keywords {
            self.add_text_to_index_runtime(keyword, paper_id, &self.keyword_index).await;
        }

        // 摘要索引
        if let Some(ref abstract_text) = paper.abstract_text {
            self.add_text_to_index_runtime(abstract_text, paper_id, &self.abstract_index).await;
        }

        // 年份索引
        if let Some(year) = paper.year {
            self.year_index
                .write()
                .await
                .entry(year)
                .or_insert_with(Vec::new)
                .push(paper_id);
        }
    }
}

/// 缓存统计信息
#[derive(Debug)]
pub struct CacheStats {
    pub papers_count: usize,
    pub folders_count: usize,
    pub last_updated: Instant,
    pub is_loaded: bool,
}

/// 搜索条件（精简版）
#[derive(Debug, Default)]
pub struct SearchCriteria {
    /// 全文搜索查询词（在标题、摘要、关键词中搜索）
    pub query: Option<String>,
    /// 文件夹路径筛选
    pub folder_path: Option<String>,
    /// 年份范围筛选
    pub year_range: Option<(u32, u32)>,
    /// 排序字段
    pub sort_by: SortBy,
    /// 排序顺序
    pub order: SortOrder,
}

/// 排序字段
#[derive(Debug)]
pub enum SortBy {
    CreatedAt,
    UpdatedAt,
    Title,
    Year,
    Author,
}

impl Default for SortBy {
    fn default() -> Self {
        SortBy::CreatedAt
    }
}

impl SortBy {
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "created_at" | "created" => Some(SortBy::CreatedAt),
            "updated_at" | "updated" => Some(SortBy::UpdatedAt),
            "title" => Some(SortBy::Title),
            "year" => Some(SortBy::Year),
            "author" => Some(SortBy::Author),
            _ => None,
        }
    }

    pub fn as_str(&self) -> &'static str {
        match self {
            SortBy::CreatedAt => "created_at",
            SortBy::UpdatedAt => "updated_at",
            SortBy::Title => "title",
            SortBy::Year => "year",
            SortBy::Author => "author",
        }
    }
}

/// 排序顺序
#[derive(Debug)]
pub enum SortOrder {
    Asc,
    Desc,
}

impl Default for SortOrder {
    fn default() -> Self {
        SortOrder::Desc
    }
}

impl SortOrder {
    pub fn from_str(s: &str) -> Option<Self> {
        match s.to_lowercase().as_str() {
            "asc" | "ascending" => Some(SortOrder::Asc),
            "desc" | "descending" => Some(SortOrder::Desc),
            _ => None,
        }
    }

    pub fn as_str(&self) -> &'static str {
        match self {
            SortOrder::Asc => "asc",
            SortOrder::Desc => "desc",
        }
    }
}

/// 详细统计信息
#[derive(Debug)]
pub struct DetailedStats {
    pub total_papers: usize,
    pub total_folders: usize,
    pub papers_by_folder: Vec<(String, usize)>,
    pub papers_by_year: Vec<(u32, usize)>,
    pub top_authors: Vec<(String, usize)>,
    pub top_keywords: Vec<(String, usize)>,
    pub recent_papers: Vec<Paper>,
}

/// 文件夹树节点
#[derive(Debug, Serialize)]
pub struct FolderNode {
    pub name: String,
    pub path: String,
    pub papers_count: usize,
    pub children: Vec<FolderNode>,
}

/// 内部树节点数据结构
#[derive(Debug)]
struct TreeNodeData {
    papers_count: usize,
    children: std::collections::HashMap<String, TreeNodeData>,
}
