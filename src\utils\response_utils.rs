use salvo::prelude::*;
use salvo::fs::NamedFile;
use salvo::http::HeaderValue;
use std::path::Path;
use crate::error::AppError;
use crate::resp::ResponseExt;

/// 文件响应类型
#[derive(Debug, <PERSON>lone, Copy)]
pub enum FileResponseType {
    /// 下载文件（attachment）
    Download,
    /// 预览文件（inline）
    Preview,
}

/// 统一的文件响应处理函数
pub async fn send_file_response(
    req: &mut Request,
    depot: &mut Depot,
    res: &mut Response,
    file_path: &Path,
    filename: &str,
    response_type: FileResponseType,
) -> Result<(), AppError> {
    // 打开文件
    let mut named_file = NamedFile::open(file_path).await
        .map_err(|e| AppError::business_logic(format!("Failed to open file: {}", e)))?;

    // 设置Content-Disposition头
    let disposition_type = match response_type {
        FileResponseType::Download => "attachment",
        FileResponseType::Preview => "inline",
    };
    
    let disposition = HeaderValue::from_str(&format!("{}; filename=\"{}\"", disposition_type, filename))
        .map_err(|e| AppError::business_logic(format!("Invalid header value: {}", e)))?;
    
    named_file.set_content_disposition(disposition);

    // 写入响应
    named_file.write(req, depot, res).await;
    
    Ok(())
}

/// 发送创建成功响应的辅助函数
pub fn send_created_response<T>(res: &mut Response, data: T)
where
    T: serde::Serialize + Send,
{
    res.status_code(StatusCode::CREATED);
    res.success(data);
}

/// 发送删除成功响应的辅助函数
pub fn send_deleted_response(res: &mut Response) {
    res.status_code(StatusCode::NO_CONTENT);
    res.success_empty();
}
