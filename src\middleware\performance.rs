use salvo::prelude::*;
use std::time::Instant;

/// 性能监控中间件
#[handler]
pub async fn performance_middleware(req: &mut Request, depot: &mut Depot, res: &mut Response, ctrl: &mut FlowCtrl) {
    let start_time = Instant::now();
    let method = req.method().to_string();
    let path = req.uri().path().to_string();

    // 继续处理请求
    ctrl.call_next(req, depot, res).await;

    let duration = start_time.elapsed();
    let status = res.status_code.unwrap_or(StatusCode::OK);

    // 记录性能日志
    if duration.as_millis() > 100 {
        tracing::warn!("⚠️  慢请求: {} {} - {}ms - {}",
                method, path, duration.as_millis(), status);
    } else {
        tracing::info!("✅ {} {} - {}ms - {}",
                method, path, duration.as_millis(), status);
    }

    // 添加性能头信息
    res.headers_mut().insert(
        "X-Response-Time",
        format!("{}ms", duration.as_millis()).parse().unwrap()
    );
}
