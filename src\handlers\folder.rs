use crate::models::folder::{CreateFolderRequest, RenameFolderRequest};
use crate::services::folder_service::FolderService;
use crate::resp::ResponseExt;
use crate::error::AppError;
use salvo::prelude::*;

#[handler]
pub async fn list_folders(req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let base_path = req.query::<String>("path");
    let folders = FolderService::list_folders(base_path.as_deref()).await?;
    res.success(folders);
    Ok(())
}

#[handler]
pub async fn create_folder(req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let request = req.parse_json::<CreateFolderRequest>().await
        .map_err(|e| AppError::request_parse(format!("Invalid request: {}", e)))?;

    let folder = FolderService::create_folder(request).await?;
    res.status_code(StatusCode::CREATED);
    res.success(folder);
    Ok(())
}

#[handler]
pub async fn delete_folder(req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let path = req.param::<String>("path").unwrap_or_default();

    FolderService::delete_folder(&path).await?;
    res.status_code(StatusCode::NO_CONTENT);
    res.success_empty();
    Ok(())
}

#[handler]
pub async fn delete_folder_force(req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let path = req.param::<String>("path").unwrap_or_default();

    let result = FolderService::delete_folder_force(&path).await?;
    res.success(result);
    Ok(())
}

#[handler]
pub async fn rename_folder(req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let path = req.param::<String>("path").unwrap_or_default();

    let request = req.parse_json::<RenameFolderRequest>().await
        .map_err(|e| AppError::request_parse(format!("Invalid request: {}", e)))?;

    let folder = FolderService::rename_folder(&path, &request.new_name).await?;
    res.success(folder);
    Ok(())
}
