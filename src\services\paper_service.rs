use crate::models::paper::{Paper, CreatePaperRequest, UpdatePaperRequest, RenameFileRequest, PaperFile, FileType};
use crate::utils::file_utils;
use crate::error::{AppError, AppResult};
use std::path::{Path, PathBuf};
use std::io;
use tokio::fs;
use uuid::Uuid;
use chrono::Utc;
use std::collections::VecDeque;

pub struct PaperService;

impl PaperService {
    pub async fn create_paper(request: CreatePaperRequest) -> AppResult<Paper> {
        let paper = Paper::new(request.clone());

        // 使用CreatePaperRequest的存储路径方法
        let storage_path = PathBuf::from(request.get_storage_path());
        let paper_dir = storage_path.join(paper.id.to_string());

        // 创建文献文件夹结构
        Self::create_paper_directory_structure(&paper_dir).await?;

        // 保存元数据到磁盘
        Self::save_paper_metadata(&paper, &paper_dir).await?;

        // 更新缓存（使用新的方法，提供物理路径）
        let cache = crate::get_cache();
        cache.add_paper_with_path(paper.clone(), paper_dir.clone()).await;

        Ok(paper)
    }

    /// 创建Paper的目录结构
    async fn create_paper_directory_structure(paper_dir: &PathBuf) -> AppResult<()> {
        fs::create_dir_all(paper_dir).await?;
        fs::create_dir_all(paper_dir.join("images")).await?;
        fs::create_dir_all(paper_dir.join("notes")).await?;
        Ok(())
    }

    pub async fn get_paper(id: &str) -> AppResult<Paper> {
        let paper_id = Uuid::parse_str(id)?;

        let cache = crate::get_cache();
        if let Some(paper) = cache.get_paper(&paper_id).await {
            return Ok(paper);
        }

        Err(AppError::paper_not_found(id))
    }

    /// 获取Paper和其物理路径的统一方法
    async fn get_paper_with_path(id: &str) -> AppResult<(Paper, PathBuf)> {
        let paper_id = Uuid::parse_str(id)?;

        let cache = crate::get_cache();
        if let Some((paper, path)) = cache.get_paper_with_path(&paper_id).await {
            return Ok((paper, path));
        }

        Err(AppError::paper_not_found(id))
    }

    pub async fn update_paper(id: &str, request: UpdatePaperRequest) -> AppResult<Paper> {
        let (mut paper, old_paper_path) = Self::get_paper_with_path(id).await?;

        // 检查是否需要移动文件夹
        let needs_move = request.needs_folder_move();

        // 在移动之前先保存旧的文件夹路径
        let old_folder_path = if needs_move {
            Some(paper.get_folder_path(&old_paper_path))
        } else {
            None
        };

        let new_paper_path = if needs_move {
            // 计算新的文件夹路径
            let config = crate::config::get_config();
            let papers_dir = PathBuf::from(&config.storage.papers_directory);

            let new_folder_path = request.get_relative_folder_path().unwrap_or_default();
            let new_parent_dir = if new_folder_path.is_empty() {
                papers_dir
            } else {
                papers_dir.join(&new_folder_path)
            };

            let paper_id = paper.id.to_string();
            let new_paper_path = new_parent_dir.join(&paper_id);

            // 如果新路径与旧路径不同，执行移动
            if new_paper_path != old_paper_path {
                Self::move_paper_directory(&old_paper_path, &new_paper_path).await?;
                new_paper_path
            } else {
                old_paper_path
            }
        } else {
            old_paper_path
        };

        // 更新Paper的元数据（不包括folder_path，因为它是从物理路径推导的）
        paper.update(request);

        // 保存到磁盘（使用新路径）
        Self::save_paper_metadata(&paper, &new_paper_path).await?;

        // 更新缓存
        let cache = crate::get_cache();
        if needs_move {
            // 如果移动了文件夹，需要手动更新文件夹索引
            let old_folder_path = old_folder_path.unwrap();
            let new_folder_path = paper.get_folder_path(&new_paper_path);

            // 更新缓存中的路径映射
            cache.update_paper_path(&paper.id, new_paper_path).await;

            // 手动更新文件夹索引
            cache.update_folder_index(&paper.id, &old_folder_path, &new_folder_path).await;
        }
        cache.update_paper(paper.clone()).await;

        Ok(paper)
    }

    pub async fn delete_paper(id: &str) -> AppResult<()> {
        let paper_id = Uuid::parse_str(id)?;

        // 获取路径（只需要路径，不需要Paper对象）
        let cache = crate::get_cache();
        let paper_path = cache.get_paper_path(&paper_id).await
            .ok_or_else(|| AppError::paper_not_found(id))?;

        // 从磁盘删除
        fs::remove_dir_all(paper_path).await?;

        // 从缓存删除
        cache.remove_paper(&paper_id).await;

        Ok(())
    }

    pub async fn list_files(id: &str) -> AppResult<Vec<PaperFile>> {
        let paper = Self::get_paper(id).await?;
        let mut files = paper.files.clone();

        // 按创建时间排序
        files.sort_by(|a, b| b.created_at.cmp(&a.created_at));
        Ok(files)
    }

    pub async fn upload_file(
        paper_id: &str,
        file_type: &str,
        filename: &str,
        file_data: Vec<u8>,
    ) -> AppResult<PaperFile> {
        let (mut paper, paper_path) = Self::get_paper_with_path(paper_id).await?;

        // 解析文件类型枚举
        let file_type_enum = FileType::from_str(file_type)
            .map_err(|_| AppError::invalid_file_type(file_type))?;

        // 使用枚举方法获取目录信息
        let subdirectory = file_type_enum.subdirectory();
        let target_dir = if subdirectory.is_empty() {
            paper_path.clone()
        } else {
            paper_path.join(subdirectory)
        };
        let relative_path_prefix = file_type_enum.relative_prefix();

        // 确保目标目录存在
        file_utils::ensure_dir_exists(&target_dir).await?;

        // 处理文件名
        let safe_filename = if let Some(default_name) = file_type_enum.default_filename() {
            default_name.to_string()
        } else {
            file_utils::sanitize_filename(filename)
        };

        // 验证文件类型
        let file_path = target_dir.join(&safe_filename);
        match file_type_enum {
            FileType::Origin => {
                if !file_utils::is_pdf_file(&file_path) && !safe_filename.ends_with(".pdf") {
                    return Err(AppError::business_logic("Origin file must be a PDF"));
                }
            }
            FileType::Image => {
                if !Self::is_valid_image_extension(&safe_filename) {
                    return Err(AppError::invalid_image_format(&safe_filename));
                }
            }
            FileType::Note => {
                // Note类型文件不需要特殊验证
            }
        }

        // 检查是否为覆盖操作
        let is_overwrite = paper.get_file(&safe_filename).is_some();
        if is_overwrite {
            println!("覆盖文件: {} (类型: {:?})", safe_filename, file_type_enum);
        }

        // 写入文件
        fs::write(&file_path, file_data).await?;

        // 获取文件元数据
        let metadata = fs::metadata(&file_path).await?;

        // 创建PaperFile对象
        let relative_path = format!("{}{}", relative_path_prefix, safe_filename);
        let paper_file = PaperFile {
            name: safe_filename.clone(),
            file_type: file_type_enum,
            relative_path,
            size: metadata.len(),
            created_at: if is_overwrite {
                // 如果是覆盖，保持原创建时间，但可以考虑添加修改时间字段
                paper.get_file(&safe_filename).unwrap().created_at
            } else {
                Utc::now()
            },
        };

        // 添加文件到Paper（add_file方法会自动处理覆盖）
        paper.add_file(paper_file.clone());
        Self::save_paper_metadata(&paper, &paper_path).await?;

        // 更新缓存
        let cache = crate::get_cache();
        cache.update_paper(paper).await;

        Ok(paper_file)
    }

    /// 删除文件
    pub async fn delete_file(paper_id: &str, filename: &str) -> AppResult<()> {
        let (mut paper, paper_path) = Self::get_paper_with_path(paper_id).await?;

        // 安全检查：防止路径遍历攻击
        let safe_filename = file_utils::sanitize_filename(filename);
        if safe_filename != filename {
            return Err(AppError::business_logic("Invalid filename"));
        }

        // 从Paper的files中查找文件信息
        let paper_file = paper.get_file(&safe_filename)
            .ok_or_else(|| AppError::business_logic("File not found"))?;

        // 构建完整文件路径并删除物理文件
        let file_path = paper_path.join(&paper_file.relative_path);
        if file_path.exists() {
            fs::remove_file(&file_path).await?;
        }

        // 从Paper中移除文件记录并保存到磁盘
        paper.remove_file(&safe_filename);
        Self::save_paper_metadata(&paper, &paper_path).await?;

        // 更新缓存
        let cache = crate::get_cache();
        cache.update_paper(paper).await;

        Ok(())
    }

    /// 重命名文件（不允许重命名origin.pdf）
    pub async fn rename_file(paper_id: &str, old_filename: &str, request: RenameFileRequest) -> AppResult<PaperFile> {
        let (mut paper, paper_path) = Self::get_paper_with_path(paper_id).await?;

        // 安全检查：防止路径遍历攻击
        let safe_old_filename = file_utils::sanitize_filename(old_filename);
        let safe_new_filename = file_utils::sanitize_filename(&request.new_name);

        if safe_old_filename != old_filename || safe_new_filename != request.new_name {
            return Err(AppError::business_logic("Invalid filename"));
        }

        // 从Paper的files中查找文件信息
        let old_paper_file = paper.get_file(&safe_old_filename)
            .ok_or_else(|| AppError::business_logic("File not found"))?
            .clone();

        // 不允许重命名origin.pdf
        if old_paper_file.file_type == FileType::Origin {
            return Err(AppError::business_logic("Cannot rename origin file"));
        }

        // 验证新文件名的扩展名是否与原文件匹配
        let old_extension = Path::new(&safe_old_filename)
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("");
        let new_extension = Path::new(&safe_new_filename)
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("");

        if old_extension.to_lowercase() != new_extension.to_lowercase() {
            return Err(AppError::business_logic("File extension cannot be changed"));
        }

        // 验证新文件名的类型是否正确
        if old_paper_file.file_type == FileType::Image && !Self::is_valid_image_extension(&safe_new_filename) {
            return Err(AppError::invalid_image_format(&safe_new_filename));
        }

        // 构建旧文件和新文件的路径
        let old_file_path = paper_path.join(&old_paper_file.relative_path);
        let new_relative_path = old_paper_file.relative_path.replace(&safe_old_filename, &safe_new_filename);
        let new_file_path = paper_path.join(&new_relative_path);

        // 检查新文件是否已存在
        if new_file_path.exists() {
            return Err(AppError::business_logic("File with new name already exists"));
        }

        // 重命名物理文件
        fs::rename(&old_file_path, &new_file_path).await?;

        // 在Paper中重命名文件记录
        paper.rename_file(&safe_old_filename, &safe_new_filename)
            .map_err(|e| AppError::business_logic(e))?;

        // 保存更新后的元数据
        Self::save_paper_metadata(&paper, &paper_path).await?;

        // 更新缓存
        let cache = crate::get_cache();
        cache.update_paper(paper.clone()).await;

        // 返回更新后的文件信息
        let updated_file = paper.get_file(&safe_new_filename)
            .ok_or_else(|| AppError::business_logic("Failed to get updated file info"))?
            .clone();

        Ok(updated_file)
    }

    fn is_valid_image_extension(filename: &str) -> bool {
        let path = Path::new(filename);
        file_utils::is_image_file(path)
    }

    /// 移动Paper目录到新位置
    async fn move_paper_directory(old_path: &Path, new_path: &Path) -> AppResult<()> {
        // 确保新的父目录存在
        if let Some(parent) = new_path.parent() {
            crate::utils::file_utils::ensure_dir_exists(parent).await?;
        }

        // 检查新路径是否已存在
        if new_path.exists() {
            return Err(AppError::business_logic("Target directory already exists"));
        }

        // 移动整个目录
        fs::rename(old_path, new_path).await
            .map_err(|e| AppError::business_logic(&format!("Failed to move paper directory: {}", e)))?;

        Ok(())
    }

    /// 保存Paper元数据到meta.json
    async fn save_paper_metadata(paper: &Paper, paper_path: &Path) -> AppResult<()> {
        let meta_file = paper_path.join("meta.json");
        let meta_content = serde_json::to_string_pretty(paper)
            .map_err(|e| io::Error::new(io::ErrorKind::InvalidData, e))?;
        fs::write(meta_file, meta_content).await?;
        Ok(())
    }

    /// 获取文件信息用于下载
    pub async fn get_file_for_download(paper_id: &str, filename: &str) -> AppResult<(PathBuf, String, u64)> {
        let (paper, paper_path) = Self::get_paper_with_path(paper_id).await?;

        // 安全检查：防止路径遍历攻击
        let safe_filename = file_utils::sanitize_filename(filename);
        if safe_filename != filename {
            return Err(AppError::business_logic("Invalid filename"));
        }

        // 从Paper的files中查找文件信息
        let paper_file = paper.get_file(&safe_filename)
            .ok_or_else(|| AppError::business_logic("File not found"))?;

        // 构建完整文件路径
        let file_path = paper_path.join(&paper_file.relative_path);

        // 验证文件是否存在
        if !file_path.exists() {
            return Err(AppError::business_logic("File not found on disk"));
        }

        let mime_type = file_utils::get_mime_type(&file_path);
        Ok((file_path, mime_type.to_string(), paper_file.size))
    }

    /// 检查文件是否可以预览
    pub async fn is_file_previewable(paper_id: &str, filename: &str) -> AppResult<bool> {
        let (paper, paper_path) = Self::get_paper_with_path(paper_id).await?;

        let safe_filename = file_utils::sanitize_filename(filename);
        if safe_filename != filename {
            return Ok(false);
        }

        // 从Paper的files中查找文件信息
        let paper_file = paper.get_file(&safe_filename);
        if paper_file.is_none() {
            return Ok(false);
        }

        let file_path = paper_path.join(&paper_file.unwrap().relative_path);
        Ok(file_utils::is_previewable(&file_path))
    }

    /// 迁移现有Paper，扫描文件系统并将文件信息添加到meta.json中
    pub async fn migrate_paper_files(paper_id: &str) -> AppResult<()> {
        let (mut paper, paper_path) = Self::get_paper_with_path(paper_id).await?;

        // 如果Paper已经有文件记录，跳过迁移
        if !paper.files.is_empty() {
            return Ok(());
        }

        let mut files = Vec::new();

        // 检查origin.pdf
        let origin_filename = FileType::Origin.default_filename().unwrap();
        let origin_file = paper_path.join(origin_filename);
        if origin_file.exists() {
            let metadata = fs::metadata(&origin_file).await?;
            files.push(PaperFile {
                name: origin_filename.to_string(),
                file_type: FileType::Origin,
                relative_path: origin_filename.to_string(),
                size: metadata.len(),
                created_at: metadata.created()
                    .unwrap_or(std::time::SystemTime::UNIX_EPOCH)
                    .into(),
            });
        }

        // 扫描images文件夹
        let images_dir = paper_path.join(FileType::Image.subdirectory());
        if images_dir.exists() {
            Self::scan_directory_for_migration(&images_dir, FileType::Image, FileType::Image.relative_prefix(), &mut files).await?;
        }

        // 扫描notes文件夹
        let notes_dir = paper_path.join(FileType::Note.subdirectory());
        if notes_dir.exists() {
            Self::scan_directory_for_migration(&notes_dir, FileType::Note, FileType::Note.relative_prefix(), &mut files).await?;
        }

        // 将文件添加到Paper并保存
        for file in files {
            paper.add_file(file);
        }
        Self::save_paper_metadata(&paper, &paper_path).await?;

        Ok(())
    }

    /// 扫描目录中的文件用于迁移
    async fn scan_directory_for_migration(
        dir: &Path,
        file_type: FileType,
        relative_prefix: &str,
        files: &mut Vec<PaperFile>
    ) -> io::Result<()> {
        let mut entries = fs::read_dir(dir).await?;

        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();

            if path.is_file() {
                let metadata = fs::metadata(&path).await?;
                let name = path.file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("")
                    .to_string();

                let relative_path = format!("{}{}", relative_prefix, name);
                files.push(PaperFile {
                    name,
                    file_type: file_type.clone(),
                    relative_path,
                    size: metadata.len(),
                    created_at: metadata.created()
                        .unwrap_or(std::time::SystemTime::UNIX_EPOCH)
                        .into(),
                });
            }
        }

        Ok(())
    }

    /// 迁移所有Paper的文件信息
    pub async fn migrate_all_papers() -> AppResult<u32> {
        let config = crate::config::get_config();
        let papers_dir = PathBuf::from(&config.storage.papers_directory);
        let mut migrated_count = 0;

        let mut dirs_to_process = VecDeque::new();
        dirs_to_process.push_back(papers_dir);

        while let Some(current_dir) = dirs_to_process.pop_front() {
            if !current_dir.exists() {
                continue;
            }

            let mut entries = fs::read_dir(&current_dir).await?;
            while let Some(entry) = entries.next_entry().await? {
                let path = entry.path();

                if path.is_dir() {
                    let name = path.file_name()
                        .and_then(|n| n.to_str())
                        .unwrap_or("");

                    // 如果是UUID命名的文件夹，尝试迁移
                    if let Ok(paper_id) = Uuid::parse_str(name) {
                        let meta_file = path.join("meta.json");
                        if meta_file.exists() {
                            if let Err(e) = Self::migrate_paper_files(&paper_id.to_string()).await {
                                eprintln!("Failed to migrate paper {}: {}", paper_id, e);
                            } else {
                                migrated_count += 1;
                            }
                        }
                    } else {
                        // 添加到待处理队列
                        dirs_to_process.push_back(path);
                    }
                }
            }
        }

        Ok(migrated_count)
    }
}
