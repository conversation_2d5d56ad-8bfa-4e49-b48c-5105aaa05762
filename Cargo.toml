[package]
name = "lite-papers"
version = "0.1.0"
edition = "2024"

[dependencies]
chrono = { version = "0.4.41", features = ["serde"] }
multer = "3.1.0"
salvo = "0.79.0"
serde = { version = "1.0.219", features = ["derive"] }
serde_json = "1.0.140"
thiserror = "2.0.12"
tokio = { version = "1.45.1", features = ["full"] }
toml = "0.8"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
uuid = { version = "1.17.0", features = ["v4", "serde"] }
