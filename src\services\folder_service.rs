use crate::models::folder::{F<PERSON>er, CreateFolderRequest, FolderDeleteResult};
use crate::error::{AppError, AppResult};
use std::path::PathBuf;
use tokio::fs;

pub struct FolderService;

impl FolderService {
    pub async fn list_folders(base_path: Option<&str>) -> AppResult<Vec<Folder>> {
        let cache = crate::get_cache();

        // 确保缓存已加载
        if !cache.is_loaded() {
            cache.initialize().await?;
        }

        // 获取文件夹树
        let folder_tree = cache.get_folder_tree().await;

        // 如果指定了基础路径，过滤出该路径下的文件夹
        if let Some(base_path) = base_path {
            // 查找指定路径的文件夹
            for node in &folder_tree {
                if node.path == base_path {
                    return Ok(node.subfolders.clone());
                }
                // 递归查找子文件夹
                if let Some(found) = Self::find_folder_in_tree(&node.subfolders, base_path) {
                    return Ok(found.subfolders.clone());
                }
            }
            // 如果没找到指定路径，返回空列表
            Ok(Vec::new())
        } else {
            // 返回所有顶级文件夹
            Ok(folder_tree)
        }
    }

    pub async fn create_folder(request: CreateFolderRequest) -> AppResult<Folder> {
        let config = crate::config::get_config();
        let papers_dir = PathBuf::from(&config.storage.papers_directory);
        let folder_path = if let Some(parent) = &request.parent_path {
            papers_dir.join(parent).join(&request.name)
        } else {
            papers_dir.join(&request.name)
        };

        if folder_path.exists() {
            return Err(AppError::folder_already_exists(
                folder_path.to_string_lossy().to_string()
            ));
        }

        fs::create_dir_all(&folder_path).await?;

        // 通知缓存更新（文件夹树会在下次访问时重新扫描）
        let cache = crate::get_cache();
        cache.touch().await;

        Ok(Folder::from_physical_path(request.name, folder_path))
    }

    /// 安全删除文件夹（只删除空文件夹）
    pub async fn delete_folder(folder_path: &str) -> AppResult<()> {
        let config = crate::config::get_config();
        let papers_dir = PathBuf::from(&config.storage.papers_directory);
        let target_path = papers_dir.join(folder_path);

        if !target_path.exists() {
            return Err(AppError::folder_not_found(folder_path));
        }

        // 检查文件夹是否包含文献
        let cache = crate::get_cache();
        let (has_papers, papers_count) = cache.has_papers_in_folder(folder_path).await;

        if has_papers {
            return Err(AppError::folder_not_empty(folder_path.to_string(), papers_count));
        }

        // 只删除空文件夹
        fs::remove_dir_all(target_path).await?;

        // 更新缓存时间戳
        cache.touch().await;

        Ok(())
    }

    /// 强制删除文件夹（包括其中的所有文献）
    pub async fn delete_folder_force(folder_path: &str) -> AppResult<FolderDeleteResult> {
        let config = crate::config::get_config();
        let papers_dir = PathBuf::from(&config.storage.papers_directory);
        let target_path = papers_dir.join(folder_path);

        if !target_path.exists() {
            return Err(AppError::folder_not_found(folder_path));
        }

        // 从缓存中删除该文件夹下的所有文献
        let cache = crate::get_cache();
        let removed_paper_ids = cache.remove_folder_and_papers(folder_path).await?;

        // 删除文件系统中的文件夹
        fs::remove_dir_all(target_path).await?;

        let result = FolderDeleteResult {
            folder_path: folder_path.to_string(),
            deleted_papers_count: removed_paper_ids.len(),
            deleted_paper_ids: removed_paper_ids,
            force_delete: true,
        };

        tracing::info!("强制删除文件夹 '{}' 及其 {} 篇文献", folder_path, result.deleted_papers_count);

        Ok(result)
    }

    pub async fn rename_folder(old_path: &str, new_name: &str) -> AppResult<Folder> {
        let config = crate::config::get_config();
        let papers_dir = PathBuf::from(&config.storage.papers_directory);
        let old_full_path = papers_dir.join(old_path);

        if !old_full_path.exists() {
            return Err(AppError::folder_not_found(old_path));
        }

        let parent = old_full_path.parent().unwrap_or(&papers_dir);
        let new_full_path = parent.join(new_name);

        if new_full_path.exists() {
            return Err(AppError::folder_already_exists(
                new_full_path.to_string_lossy().to_string()
            ));
        }

        // 计算新的文件夹路径（相对于papers目录）
        let new_path = if let Ok(relative_path) = new_full_path.strip_prefix(&papers_dir) {
            relative_path.to_string_lossy().to_string()
        } else {
            new_name.to_string()
        };

        // 重命名文件系统中的文件夹
        fs::rename(&old_full_path, &new_full_path).await?;

        // 更新缓存中的文件夹路径（不再需要更新meta.json文件）
        let cache = crate::get_cache();
        cache.update_folder_path(old_path, &new_path).await?;

        Ok(Folder::new(new_name.to_string(), new_path))
    }

    /// 在文件夹树中递归查找指定路径的文件夹
    fn find_folder_in_tree<'a>(nodes: &'a [Folder], target_path: &str) -> Option<&'a Folder> {
        for node in nodes {
            if node.path == target_path {
                return Some(node);
            }
            if let Some(found) = Self::find_folder_in_tree(&node.subfolders, target_path) {
                return Some(found);
            }
        }
        None
    }




}
