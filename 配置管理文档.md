# 配置管理文档

## 概述

Lite Papers 支持通过 `config.toml` 文件进行配置管理，提供灵活的系统参数调整能力。

## 配置文件位置

配置文件应放置在项目根目录下，文件名为 `config.toml`。

## 配置加载策略

1. **启动时检查**：系统启动时会自动检查是否存在 `config.toml` 文件
2. **配置文件存在**：加载并使用配置文件中的设置
3. **配置文件不存在**：使用内置的默认配置
4. **配置验证**：加载后会验证配置的有效性

## 配置文件结构

### 完整配置示例

```toml
# Lite Papers 配置文件

[server]
host = "127.0.0.1"
port = 3000
name = "Lite Papers"
description = "轻量级科研文献管理系统"

[storage]
papers_directory = "papers"
allowed_image_formats = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"]
allowed_document_formats = ["pdf", "txt", "md", "json"]

[pagination]
default_page_size = 10
max_page_size = 100
min_page_size = 1

[search]
default_sort_by = "created_at"
default_sort_order = "desc"
min_word_length = 2

[stats]
top_authors_limit = 10
top_keywords_limit = 20
recent_papers_limit = 10

[cache]
verbose_logging = true
```

## 配置项详解

### [server] - 服务器配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `host` | string | "127.0.0.1" | 服务器监听地址 |
| `port` | number | 3000 | 服务器监听端口 |
| `name` | string | "Lite Papers" | 应用名称 |
| `description` | string | "轻量级科研文献管理系统" | 应用描述 |

### [storage] - 存储配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `papers_directory` | string | "papers" | 文献存储目录 |
| `allowed_image_formats` | array | ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"] | 支持的图片格式 |
| `allowed_document_formats` | array | ["pdf", "txt", "md", "json"] | 支持的文档格式 |

### [pagination] - 分页配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `default_page_size` | number | 10 | 默认每页大小 |
| `max_page_size` | number | 100 | 最大每页大小 |
| `min_page_size` | number | 1 | 最小每页大小 |

### [search] - 搜索配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `default_sort_by` | string | "created_at" | 默认排序字段 |
| `default_sort_order` | string | "desc" | 默认排序顺序 |
| `min_word_length` | number | 2 | 分词最小长度 |

**支持的排序字段**：
- `created_at` - 创建时间
- `updated_at` - 更新时间
- `title` - 标题
- `year` - 年份
- `author` - 作者

**支持的排序顺序**：
- `asc` - 升序
- `desc` - 降序

### [stats] - 统计配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `top_authors_limit` | number | 10 | 热门作者显示数量 |
| `top_keywords_limit` | number | 20 | 热门关键词显示数量 |
| `recent_papers_limit` | number | 10 | 最近文献显示数量 |

### [cache] - 缓存配置

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `verbose_logging` | boolean | true | 是否启用详细日志 |

## 配置验证规则

系统会在启动时验证配置的有效性：

1. **端口验证**：端口号不能为0
2. **分页验证**：
   - 默认页大小不能大于最大页大小
   - 最小页大小不能为0
3. **目录验证**：文献目录路径不能为空
4. **搜索验证**：分词最小长度不能为0

## 使用示例

### 1. 修改服务器端口

```toml
[server]
host = "0.0.0.0"
port = 8080
```

### 2. 调整分页设置

```toml
[pagination]
default_page_size = 20
max_page_size = 200
```

### 3. 自定义文件格式支持

```toml
[storage]
allowed_image_formats = ["jpg", "png", "webp"]
allowed_document_formats = ["pdf", "txt", "docx"]
```

### 4. 调整统计显示数量

```toml
[stats]
top_authors_limit = 15
top_keywords_limit = 30
recent_papers_limit = 5
```

## 配置生效

- **立即生效**：修改配置文件后，重启应用即可生效
- **无需重新编译**：配置修改不需要重新编译代码
- **向后兼容**：缺少的配置项会使用默认值

## 故障排除

### 配置文件格式错误

如果配置文件格式有误，系统会显示详细的错误信息：

```
配置初始化失败: invalid TOML value, did you mean to use a quoted string? at line 5 column 10
```

### 配置验证失败

如果配置值不符合验证规则：

```
配置初始化失败: Default page size cannot be larger than max page size
```

### 解决方案

1. 检查TOML语法是否正确
2. 确保配置值符合验证规则
3. 删除配置文件使用默认配置
4. 参考本文档的配置示例

## 最佳实践

1. **备份配置**：修改前备份原配置文件
2. **逐步调整**：一次只修改少量配置项
3. **测试验证**：修改后测试相关功能是否正常
4. **文档记录**：记录重要的配置修改

## 技术实现

- **配置格式**：TOML (Tom's Obvious, Minimal Language)
- **加载机制**：启动时一次性加载，运行时静态访问
- **验证机制**：类型安全 + 业务规则验证
- **默认值**：完整的默认配置，确保系统可用性
