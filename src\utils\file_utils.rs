use std::path::{Path, PathBuf};
use std::io;
use tokio::fs;
use uuid::Uuid;
use crate::error::AppResult;

pub async fn ensure_dir_exists(path: &Path) -> io::Result<()> {
    if !path.exists() {
        fs::create_dir_all(path).await?;
    }
    Ok(())
}

pub fn get_file_extension(path: &Path) -> Option<String> {
    path.extension()
        .and_then(|ext| ext.to_str())
        .map(|s| s.to_lowercase())
}

pub fn is_image_file(path: &Path) -> bool {
    match get_file_extension(path) {
        Some(ext) => crate::config::get_config().storage.allowed_image_formats.contains(&ext),
        None => false,
    }
}

pub fn is_pdf_file(path: &Path) -> bool {
    match get_file_extension(path) {
        Some(ext) => ext == "pdf",
        None => false,
    }
}

pub fn sanitize_filename(filename: &str) -> String {
    filename
        .chars()
        .map(|c| match c {
            '<' | '>' | ':' | '"' | '|' | '?' | '*' | '\\' | '/' => '_',
            c if c.is_control() => '_',
            c => c,
        })
        .collect()
}

/// 根据文件扩展名获取 MIME 类型
pub fn get_mime_type(path: &Path) -> &'static str {
    match get_file_extension(path) {
        Some(ext) => match ext.as_str() {
            // 图片类型
            "jpg" | "jpeg" => "image/jpeg",
            "png" => "image/png",
            "gif" => "image/gif",
            "bmp" => "image/bmp",
            "webp" => "image/webp",
            "svg" => "image/svg+xml",

            // 文档类型
            "pdf" => "application/pdf",
            "txt" => "text/plain",
            "md" => "text/markdown",
            "json" => "application/json",

            // 默认类型
            _ => "application/octet-stream",
        },
        None => "application/octet-stream",
    }
}

/// 检查文件是否可以在浏览器中预览
pub fn is_previewable(path: &Path) -> bool {
    match get_file_extension(path) {
        Some(ext) => {
            let config = crate::config::get_config();
            config.storage.allowed_image_formats.contains(&ext) ||
            config.storage.allowed_document_formats.contains(&ext)
        },
        None => false,
    }
}

/// 检查目录名是否为UUID格式（用于识别文献文件夹）
pub fn is_uuid_folder(name: &str) -> bool {
    Uuid::parse_str(name).is_ok()
}

/// 统一的文件夹遍历函数，用于获取非UUID命名的子文件夹
pub async fn get_non_uuid_subfolders(folder_path: &Path) -> AppResult<Vec<PathBuf>> {
    let mut subfolders = Vec::new();

    if !folder_path.exists() {
        return Ok(subfolders);
    }

    let mut entries = fs::read_dir(folder_path).await?;

    while let Some(entry) = entries.next_entry().await? {
        let path = entry.path();

        if path.is_dir() {
            let name = path.file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("");

            // 跳过以UUID命名的文献文件夹
            if !is_uuid_folder(name) {
                subfolders.push(path);
            }
        }
    }

    Ok(subfolders)
}

/// 统一的文献文件夹计数函数
pub async fn count_paper_folders(folder_path: &Path) -> AppResult<usize> {
    let mut count = 0;

    if !folder_path.exists() {
        return Ok(count);
    }

    let mut entries = fs::read_dir(folder_path).await?;

    while let Some(entry) = entries.next_entry().await? {
        let path = entry.path();

        if path.is_dir() {
            let name = path.file_name()
                .and_then(|n| n.to_str())
                .unwrap_or("");

            // 检查是否是UUID命名的文献文件夹
            if is_uuid_folder(name) {
                let meta_file = path.join("meta.json");
                if meta_file.exists() {
                    count += 1;
                }
            }
        }
    }

    Ok(count)
}
