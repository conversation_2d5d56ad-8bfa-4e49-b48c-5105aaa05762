use std::path::Path;
use std::io;
use tokio::fs;
use uuid::Uuid;

pub async fn ensure_dir_exists(path: &Path) -> io::Result<()> {
    if !path.exists() {
        fs::create_dir_all(path).await?;
    }
    Ok(())
}

pub fn get_file_extension(path: &Path) -> Option<String> {
    path.extension()
        .and_then(|ext| ext.to_str())
        .map(|s| s.to_lowercase())
}

pub fn is_image_file(path: &Path) -> bool {
    match get_file_extension(path) {
        Some(ext) => crate::config::get_config().storage.allowed_image_formats.contains(&ext),
        None => false,
    }
}

pub fn is_pdf_file(path: &Path) -> bool {
    match get_file_extension(path) {
        Some(ext) => ext == "pdf",
        None => false,
    }
}

pub fn sanitize_filename(filename: &str) -> String {
    filename
        .chars()
        .map(|c| match c {
            '<' | '>' | ':' | '"' | '|' | '?' | '*' | '\\' | '/' => '_',
            c if c.is_control() => '_',
            c => c,
        })
        .collect()
}

/// 根据文件扩展名获取 MIME 类型
pub fn get_mime_type(path: &Path) -> &'static str {
    match get_file_extension(path) {
        Some(ext) => match ext.as_str() {
            // 图片类型
            "jpg" | "jpeg" => "image/jpeg",
            "png" => "image/png",
            "gif" => "image/gif",
            "bmp" => "image/bmp",
            "webp" => "image/webp",
            "svg" => "image/svg+xml",

            // 文档类型
            "pdf" => "application/pdf",
            "txt" => "text/plain",
            "md" => "text/markdown",
            "json" => "application/json",

            // 默认类型
            _ => "application/octet-stream",
        },
        None => "application/octet-stream",
    }
}

/// 检查文件是否可以在浏览器中预览
pub fn is_previewable(path: &Path) -> bool {
    match get_file_extension(path) {
        Some(ext) => {
            let config = crate::config::get_config();
            config.storage.allowed_image_formats.contains(&ext) ||
            config.storage.allowed_document_formats.contains(&ext)
        },
        None => false,
    }
}

/// 检查目录名是否为UUID格式（用于识别文献文件夹）
pub fn is_uuid_folder(name: &str) -> bool {
    Uuid::parse_str(name).is_ok()
}


