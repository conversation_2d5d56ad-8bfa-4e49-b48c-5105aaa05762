use thiserror::Error;
use salvo::prelude::*;
use std::io;
use uuid;
use serde_json;
use crate::resp::ResponseExt;

/// 应用程序错误类型
#[derive(Error, Debug)]
pub enum AppError {
    /// IO错误
    #[error("IO error: {0}")]
    Io(#[from] io::Error),
    
    /// JSON序列化/反序列化错误
    #[error("JSON error: {0}")]
    Json(#[from] serde_json::Error),
    
    /// UUID解析错误
    #[error("UUID parse error: {0}")]
    UuidParse(#[from] uuid::Error),
    
    /// 文献未找到
    #[error("Paper not found: {id}")]
    PaperNotFound { id: String },
    
    /// 文件夹未找到
    #[error("Folder not found: {path}")]
    FolderNotFound { path: String },
    
    /// 文件夹已存在
    #[error("Folder already exists: {path}")]
    FolderAlreadyExists { path: String },

    /// 文件夹非空（包含文献）
    #[error("Folder is not empty: {path} contains {papers_count} paper(s). Use force delete to remove folder with all papers.")]
    FolderNotEmpty { path: String, papers_count: usize },
    
    /// 无效的文件类型
    #[error("Invalid file type: {file_type}. Must be 'image', 'note', or 'origin'")]
    InvalidFileType { file_type: String },
    
    /// 无效的图片格式
    #[error("Invalid image format: {filename}. Supported formats: jpg, jpeg, png, gif, bmp, webp, svg")]
    InvalidImageFormat { filename: String },
    
    /// 文件上传错误
    #[error("File upload error: {message}")]
    FileUpload { message: String },
    
    /// 请求解析错误
    #[error("Request parse error: {message}")]
    RequestParse { message: String },

    /// 业务逻辑错误
    #[error("Business logic error: {message}")]
    BusinessLogic { message: String },
}

/// 应用程序结果类型
pub type AppResult<T> = Result<T, AppError>;

impl AppError {
    /// 创建文献未找到错误
    pub fn paper_not_found(id: impl Into<String>) -> Self {
        Self::PaperNotFound { id: id.into() }
    }
    
    /// 创建文件夹未找到错误
    pub fn folder_not_found(path: impl Into<String>) -> Self {
        Self::FolderNotFound { path: path.into() }
    }
    
    /// 创建文件夹已存在错误
    pub fn folder_already_exists(path: impl Into<String>) -> Self {
        Self::FolderAlreadyExists { path: path.into() }
    }

    /// 创建文件夹非空错误
    pub fn folder_not_empty(path: impl Into<String>, papers_count: usize) -> Self {
        Self::FolderNotEmpty {
            path: path.into(),
            papers_count
        }
    }
    
    /// 创建无效文件类型错误
    pub fn invalid_file_type(file_type: impl Into<String>) -> Self {
        Self::InvalidFileType { file_type: file_type.into() }
    }
    
    /// 创建无效图片格式错误
    pub fn invalid_image_format(filename: impl Into<String>) -> Self {
        Self::InvalidImageFormat { filename: filename.into() }
    }
    
    /// 创建文件上传错误
    pub fn file_upload(message: impl Into<String>) -> Self {
        Self::FileUpload { message: message.into() }
    }
    
    /// 创建请求解析错误
    pub fn request_parse(message: impl Into<String>) -> Self {
        Self::RequestParse { message: message.into() }
    }

    /// 创建业务逻辑错误
    pub fn business_logic(message: impl Into<String>) -> Self {
        Self::BusinessLogic { message: message.into() }
    }
    
    /// 获取错误对应的HTTP状态码
    pub fn status_code(&self) -> StatusCode {
        match self {
            AppError::PaperNotFound { .. } => StatusCode::NOT_FOUND,
            AppError::FolderNotFound { .. } => StatusCode::NOT_FOUND,
            AppError::FolderAlreadyExists { .. } => StatusCode::CONFLICT,
            AppError::FolderNotEmpty { .. } => StatusCode::CONFLICT,
            AppError::InvalidFileType { .. } => StatusCode::BAD_REQUEST,
            AppError::InvalidImageFormat { .. } => StatusCode::BAD_REQUEST,
            AppError::RequestParse { .. } => StatusCode::BAD_REQUEST,
            AppError::UuidParse(_) => StatusCode::BAD_REQUEST,
            AppError::Json(_) => StatusCode::BAD_REQUEST,
            AppError::FileUpload { .. } => StatusCode::BAD_REQUEST,
            AppError::BusinessLogic { .. } => StatusCode::BAD_REQUEST,
            AppError::Io(_) => StatusCode::INTERNAL_SERVER_ERROR,
        }
    }
    
    /// 获取错误代码
    pub fn error_code(&self) -> i32 {
        self.status_code().as_u16() as i32
    }
}

/// 为AppError实现Writer trait，使其可以直接作为Handler的返回值
#[async_trait]
impl Writer for AppError {
    async fn write(mut self, _req: &mut Request, _depot: &mut Depot, res: &mut Response) {
        let error_code = self.error_code();
        let message = self.to_string();

        res.error(error_code, message);
    }
}

/// 便捷宏，用于快速创建和返回错误
#[macro_export]
macro_rules! app_error {
    (paper_not_found, $id:expr) => {
        $crate::error::AppError::paper_not_found($id)
    };
    (folder_not_found, $path:expr) => {
        $crate::error::AppError::folder_not_found($path)
    };
    (folder_already_exists, $path:expr) => {
        $crate::error::AppError::folder_already_exists($path)
    };
    (folder_not_empty, $path:expr, $count:expr) => {
        $crate::error::AppError::folder_not_empty($path, $count)
    };
    (invalid_file_type, $file_type:expr) => {
        $crate::error::AppError::invalid_file_type($file_type)
    };
    (invalid_image_format, $filename:expr) => {
        $crate::error::AppError::invalid_image_format($filename)
    };
    (file_upload, $msg:expr) => {
        $crate::error::AppError::file_upload($msg)
    };
    (request_parse, $msg:expr) => {
        $crate::error::AppError::request_parse($msg)
    };
    (business_logic, $msg:expr) => {
        $crate::error::AppError::business_logic($msg)
    };
}

/// 便捷宏，用于快速返回错误结果
#[macro_export]
macro_rules! app_bail {
    ($error_type:ident, $($arg:expr),*) => {
        return Err($crate::app_error!($error_type, $($arg),*))
    };
}
