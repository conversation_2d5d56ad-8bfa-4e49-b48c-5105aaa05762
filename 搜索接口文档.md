# 统一搜索接口文档

## 概述

已将所有搜索功能统一为一个智能搜索接口，支持全文搜索与条件过滤的组合使用。

## API 端点

### 统一搜索接口

**端点**: `GET /api/papers/search`

**功能**: 智能搜索接口，支持全文搜索与条件过滤的组合使用，所有搜索结果都支持分页和自定义排序。

### 请求参数

#### 搜索条件参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `q` | string | 否 | 全文搜索关键词（在标题、摘要、关键词中搜索） |
| `title` | string | 否 | 标题搜索关键词 |
| `author` | string | 否 | 作者搜索关键词 |
| `keywords` | string | 否 | 关键词搜索（支持逗号分隔的多个关键词） |
| `abstract` | string | 否 | 摘要搜索关键词 |
| `folder` | string | 否 | 文件夹路径过滤 |
| `year_start` | number | 否 | 年份范围开始 |
| `year_end` | number | 否 | 年份范围结束 |
| `sort_by` | string | 否 | 排序字段（created_at, updated_at, title, year, author） |
| `sort_order` | string | 否 | 排序顺序（asc, desc） |

#### 分页参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `page` | number | 否 | 1 | 页码（从1开始） |
| `page_size` | number | 否 | 10 | 每页大小（最小1，最大100） |

#### 排序参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `sort_by` | string | 否 | created_at | 排序字段 |
| `sort_order` | string | 否 | desc | 排序顺序 |

**支持的排序字段**：
- `created_at` / `created` - 按创建时间排序
- `updated_at` / `updated` - 按更新时间排序
- `title` - 按标题排序
- `year` - 按年份排序
- `author` - 按第一作者排序

**支持的排序顺序**：
- `asc` / `ascending` - 升序
- `desc` / `descending` - 降序

### 搜索逻辑

#### 智能搜索组合
系统支持全文搜索与条件过滤的智能组合：

1. **全文搜索**：当提供 `q` 参数时，在标题、摘要、关键词中搜索
2. **条件过滤**：在全文搜索结果基础上，进一步应用其他过滤条件
3. **AND 逻辑**：所有指定的过滤条件都必须同时满足

#### 搜索流程
1. 如果提供了 `q` 参数，先执行全文搜索获得候选结果
2. 在候选结果（或全部文献）基础上，应用其他过滤条件
3. 对最终结果进行排序和分页

#### 优势
- **灵活组合**：可以同时使用全文搜索和精确过滤
- **高效过滤**：先全文搜索缩小范围，再精确过滤
- **统一接口**：一个接口满足所有搜索需求

### 响应格式

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "papers": [
      {
        "id": "46ff3c2e-f3a4-474d-af68-56d61f2e4f7c",
        "title": "Attention Is All You Need",
        "authors": ["Ashish Vaswani", "Noam Shazeer"],
        "journal": "NIPS",
        "year": 2017,
        "doi": "10.48550/arXiv.1706.03762",
        "abstract_text": "The dominant sequence transduction models...",
        "keywords": ["transformer", "attention", "neural networks"],
        "folder_path": "AI Papers",
        "files": [],
        "created_at": "2025-06-10T06:33:03.470935100Z",
        "updated_at": "2025-06-10T06:33:03.470935100Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 10,
      "total": 1,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    }
  }
}
```

### 使用示例

#### 1. 全文搜索
```bash
curl "http://127.0.0.1:3000/api/papers/search?q=attention&page=1&page_size=5"
```

#### 2. 按标题和作者搜索
```bash
curl "http://127.0.0.1:3000/api/papers/search?title=attention&author=vaswani&page=1&page_size=5"
```

#### 3. 多关键词搜索
```bash
curl "http://127.0.0.1:3000/api/papers/search?keywords=transformer,attention&page=1&page_size=5"
```

#### 4. 年份范围搜索
```bash
curl "http://127.0.0.1:3000/api/papers/search?year_start=2017&year_end=2020&page=1&page_size=5"
```

#### 5. 复合条件搜索
```bash
curl "http://127.0.0.1:3000/api/papers/search?title=gpt&author=brown&year_start=2020&year_end=2021&page=1&page_size=5"
```

#### 6. 文件夹内搜索
```bash
curl "http://127.0.0.1:3000/api/papers/search?folder=AI%20Papers&title=attention&page=1&page_size=5"
```

#### 7. 按标题升序排序
```bash
curl "http://127.0.0.1:3000/api/papers/search?sort_by=title&sort_order=asc&page=1&page_size=5"
```

#### 8. 按年份降序排序
```bash
curl "http://127.0.0.1:3000/api/papers/search?sort_by=year&sort_order=desc&page=1&page_size=5"
```

#### 9. 复合搜索并按作者排序
```bash
curl "http://127.0.0.1:3000/api/papers/search?title=attention&keywords=transformer&sort_by=author&sort_order=asc&page=1&page_size=5"
```

#### 10. 全文搜索 + 条件过滤组合
```bash
# 在包含"transformer"的文献中，进一步筛选2017-2020年的文献，按年份排序
curl "http://127.0.0.1:3000/api/papers/search?q=transformer&year_start=2017&year_end=2020&sort_by=year&sort_order=desc&page=1&page_size=5"
```

#### 11. 全文搜索 + 文件夹过滤
```bash
# 在"AI Papers"文件夹中搜索包含"attention"的文献
curl "http://127.0.0.1:3000/api/papers/search?q=attention&folder=AI%20Papers&page=1&page_size=5"
```

## 性能特点

- **内存搜索**: 所有搜索操作都在内存中进行，响应速度快
- **索引优化**: 使用倒排索引加速文本搜索
- **分页支持**: 所有搜索结果都支持分页，避免大结果集的性能问题
- **缓存一致性**: 搜索结果与缓存数据保持一致

## 已移除的端点

以下端点已被统一搜索接口替代：
- `GET /api/papers/search/title` - 使用 `title` 参数替代
- `GET /api/papers/search/author` - 使用 `author` 参数替代
- `GET /api/papers/search/paginated` - 统一搜索接口本身就支持分页

## 注意事项

1. **搜索关键词**: 支持部分匹配，不区分大小写
2. **关键词分隔**: 多个关键词使用逗号分隔
3. **年份范围**: 必须同时提供 `year_start` 和 `year_end`，且开始年份不能大于结束年份
4. **分页限制**: 每页最大100条记录
5. **空搜索**: 如果不提供任何搜索条件，返回所有文献（分页）
