use crate::models::paper::{CreatePaperRequest, UpdatePaperRequest, RenameFileRequest};
use crate::services::paper_service::PaperService;
use crate::resp::ResponseExt;
use crate::error::AppError;
use crate::utils::request_utils::{PaginationParams, get_path_param, parse_json_body, validate_file_type};
use crate::utils::response_utils::{send_file_response, FileResponseType, send_created_response, send_deleted_response};
use salvo::prelude::*;

#[handler]
pub async fn list_papers(req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    // 获取核心搜索参数
    let query = req.query::<String>("q");
    let folder_path = req.query::<String>("folder");
    let year_start = req.query::<u32>("year_start");
    let year_end = req.query::<u32>("year_end");

    // 获取配置
    let config = crate::config::get_config();

    // 获取排序参数
    let sort_by_str = req.query::<String>("sort_by").unwrap_or_else(|| config.search.default_sort_by.clone());
    let sort_order_str = req.query::<String>("sort_order").unwrap_or_else(|| config.search.default_sort_order.clone());

    // 获取分页参数
    let pagination = PaginationParams::from_request(req);

    let cache = crate::get_cache();

    // 构建简化的搜索条件
    let mut criteria = crate::cache::paper_cache::SearchCriteria::default();

    // 全文搜索查询词（在标题、摘要、关键词中搜索）
    if let Some(q) = query {
        if !q.is_empty() {
            criteria.query = Some(q);
        }
    }

    // 文件夹筛选
    criteria.folder_path = folder_path;

    // 年份范围筛选
    if let (Some(start), Some(end)) = (year_start, year_end) {
        if start <= end {
            criteria.year_range = Some((start, end));
        }
    }

    // 处理排序参数
    if let Some(sort_by) = crate::cache::paper_cache::SortBy::from_str(&sort_by_str) {
        criteria.sort_by = sort_by;
    }
    if let Some(sort_order) = crate::cache::paper_cache::SortOrder::from_str(&sort_order_str) {
        criteria.order = sort_order;
    }

    // 使用统一搜索接口
    let results = cache.search_paginated(&criteria, pagination.page, pagination.page_size).await;

    res.success(results);
    Ok(())
}

#[handler]
pub async fn create_paper(req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let request = parse_json_body::<CreatePaperRequest>(req).await?;
    let paper = PaperService::create_paper(request).await?;
    send_created_response(res, paper);
    Ok(())
}

#[handler]
pub async fn get_paper(req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let id = get_path_param(req, "id")?;
    let paper = PaperService::get_paper(&id).await?;
    res.success(paper);
    Ok(())
}

#[handler]
pub async fn update_paper(req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let id = get_path_param(req, "id")?;
    let request = parse_json_body::<UpdatePaperRequest>(req).await?;
    let _paper = PaperService::update_paper(&id, request).await?;

    // 获取更新后的Paper和路径信息
    let paper_with_path = PaperService::get_paper(&id).await?;
    res.success(paper_with_path);
    Ok(())
}

#[handler]
pub async fn delete_paper(req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let id = get_path_param(req, "id")?;
    PaperService::delete_paper(&id).await?;
    send_deleted_response(res);
    Ok(())
}

#[handler]
pub async fn list_files(req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let id = req.param::<String>("id").unwrap_or_default();

    let files = PaperService::list_files(&id).await?;
    res.success(files);
    Ok(())
}

#[handler]
pub async fn upload_file(req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let paper_id = get_path_param(req, "id")?;
    let file_type = req.query::<String>("type").unwrap_or_else(|| "note".to_string());

    validate_file_type(&file_type)?;

    // 使用Salvo内置的文件处理方法
    let file = req.file("file").await
        .ok_or_else(|| AppError::file_upload("No file found in request"))?;

    let filename = file.name().unwrap_or("unknown").to_string();

    // 读取文件数据
    let file_data = tokio::fs::read(file.path()).await
        .map_err(|e| AppError::file_upload(format!("Failed to read uploaded file: {}", e)))?;

    if file_data.is_empty() {
        return Err(AppError::file_upload("No file data received"));
    }

    let paper_file = PaperService::upload_file(&paper_id, &file_type, &filename, file_data).await?;
    send_created_response(res, paper_file);
    Ok(())
}

#[handler]
pub async fn download_file(req: &mut Request, depot: &mut Depot, res: &mut Response) -> Result<(), AppError> {
    let paper_id = get_path_param(req, "id")?;
    let filename = get_path_param(req, "filename")?;

    // 获取文件信息
    let (file_path, _mime_type, _file_size) = PaperService::get_file_for_download(&paper_id, &filename).await?;

    send_file_response(req, depot, res, &file_path, &filename, FileResponseType::Download).await
}

#[handler]
pub async fn preview_file(req: &mut Request, depot: &mut Depot, res: &mut Response) -> Result<(), AppError> {
    let paper_id = get_path_param(req, "id")?;
    let filename = get_path_param(req, "filename")?;

    // 检查文件是否可以预览
    let is_previewable = PaperService::is_file_previewable(&paper_id, &filename).await?;
    if !is_previewable {
        return Err(AppError::business_logic("File cannot be previewed"));
    }

    // 获取文件信息
    let (file_path, _mime_type, _file_size) = PaperService::get_file_for_download(&paper_id, &filename).await?;

    send_file_response(req, depot, res, &file_path, &filename, FileResponseType::Preview).await
}

#[handler]
pub async fn delete_file(req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let paper_id = get_path_param(req, "id")?;
    let filename = get_path_param(req, "filename")?;

    PaperService::delete_file(&paper_id, &filename).await?;
    send_deleted_response(res);
    Ok(())
}

#[handler]
pub async fn rename_file(req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let paper_id = get_path_param(req, "id")?;
    let old_filename = get_path_param(req, "filename")?;

    let request = parse_json_body::<RenameFileRequest>(req).await?;

    let updated_file = PaperService::rename_file(&paper_id, &old_filename, request).await?;
    res.success(updated_file);
    Ok(())
}

#[handler]
pub async fn migrate_all_papers(_req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let migrated_count = PaperService::migrate_all_papers().await?;
    res.success(serde_json::json!({
        "message": "Migration completed",
        "migrated_count": migrated_count
    }));
    Ok(())
}

#[handler]
pub async fn cache_stats(_req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let cache = crate::get_cache();
    let stats = cache.get_stats().await;

    res.success(serde_json::json!({
        "papers_count": stats.papers_count,
        "folders_count": stats.folders_count,
        "is_loaded": stats.is_loaded,
        "last_updated": stats.last_updated.elapsed().as_secs()
    }));
    Ok(())
}



#[handler]
pub async fn get_detailed_stats(_req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let cache = crate::get_cache();
    let stats = cache.get_detailed_stats().await;

    res.success(serde_json::json!({
        "total_papers": stats.total_papers,
        "total_folders": stats.total_folders,
        "papers_by_folder": stats.papers_by_folder,
        "papers_by_year": stats.papers_by_year,
        "top_authors": stats.top_authors,
        "top_keywords": stats.top_keywords,
        "recent_papers_count": stats.recent_papers.len()
    }));
    Ok(())
}

#[handler]
pub async fn get_folder_tree(_req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let cache = crate::get_cache();
    let folder_tree = cache.get_folder_tree().await;

    res.success(folder_tree);
    Ok(())
}

#[handler]
pub async fn health_check(_req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let cache = crate::get_cache();
    let stats = cache.get_stats().await;

    let health_info = serde_json::json!({
        "status": "healthy",
        "cache_loaded": stats.is_loaded,
        "papers_count": stats.papers_count,
        "folders_count": stats.folders_count,
        "uptime_seconds": stats.last_updated.elapsed().as_secs(),
        "timestamp": chrono::Utc::now().to_rfc3339()
    });

    res.success(health_info);
    Ok(())
}

#[handler]
pub async fn cache_reload(_req: &mut Request, res: &mut Response) -> Result<(), AppError> {
    let cache = crate::get_cache();

    match cache.reload().await {
        Ok(_) => {
            let stats = cache.get_stats().await;
            res.success(serde_json::json!({
                "message": "Cache reloaded successfully",
                "papers_count": stats.papers_count,
                "folders_count": stats.folders_count
            }));
        }
        Err(e) => {
            return Err(AppError::business_logic(&format!("Failed to reload cache: {}", e)));
        }
    }

    Ok(())
}
