use serde::{Deserialize, Serialize};
use std::path::Path<PERSON>uf;
use uuid::Uuid;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Folder {
    pub name: String,
    pub path: PathBuf,
    pub papers_count: usize,
    pub subfolders: Vec<Folder>,
}

#[derive(Debug, Deserialize)]
pub struct CreateFolderRequest {
    pub name: String,
    pub parent_path: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct RenameFolderRequest {
    pub new_name: String,
}

impl Folder {
    pub fn new(name: String, path: PathBuf) -> Self {
        Self {
            name,
            path,
            papers_count: 0,
            subfolders: Vec::new(),
        }
    }
}

/// 文件夹删除结果
#[derive(Debug, Serialize)]
pub struct FolderDeleteResult {
    /// 删除的文件夹路径
    pub folder_path: String,
    /// 删除的文献数量
    pub deleted_papers_count: usize,
    /// 删除的文献ID列表
    pub deleted_paper_ids: Vec<Uuid>,
    /// 是否为强制删除
    pub force_delete: bool,
}
