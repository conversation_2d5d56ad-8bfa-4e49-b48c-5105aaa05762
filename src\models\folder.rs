use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use uuid::Uuid;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Folder {
    pub name: String,
    /// 虚拟路径（相对于papers目录的路径）
    pub path: String,
    pub papers_count: usize,
    pub subfolders: Vec<Folder>,
}

#[derive(Debug, Deserialize)]
pub struct CreateFolderRequest {
    pub name: String,
    pub parent_path: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct RenameFolderRequest {
    pub new_name: String,
}

impl Folder {
    pub fn new(name: String, path: String) -> Self {
        Self {
            name,
            path,
            papers_count: 0,
            subfolders: Vec::new(),
        }
    }

    /// 从物理路径创建Folder（转换为虚拟路径）
    pub fn from_physical_path(name: String, physical_path: PathBuf) -> Self {
        let config = crate::config::get_config();
        let papers_dir = PathBuf::from(&config.storage.papers_directory);

        let virtual_path = if let Ok(relative_path) = physical_path.strip_prefix(&papers_dir) {
            relative_path.to_string_lossy().to_string()
        } else {
            name.clone()
        };

        Self {
            name,
            path: virtual_path,
            papers_count: 0,
            subfolders: Vec::new(),
        }
    }


}

/// 文件夹删除结果
#[derive(Debug, Serialize)]
pub struct FolderDeleteResult {
    /// 删除的文件夹路径
    pub folder_path: String,
    /// 删除的文献数量
    pub deleted_papers_count: usize,
    /// 删除的文献ID列表
    pub deleted_paper_ids: Vec<Uuid>,
    /// 是否为强制删除
    pub force_delete: bool,
}
