use serde::{Deserialize, Serialize, Serializer, Deserializer};
use std::path::PathBuf;
use std::collections::HashMap;
use uuid::Uuid;

#[derive(Debug, Clone, Deserialize)]
pub struct Folder {
    pub name: String,
    /// 虚拟路径（相对于papers目录的路径）
    pub path: String,
    pub papers_count: usize,
    /// 子文件夹映射：文件夹名 -> Folder对象
    #[serde(deserialize_with = "deserialize_subfolders")]
    pub subfolders: HashMap<String, Folder>,
}

// 自定义序列化实现，将 HashMap 转换为 Vec 以保持 API 兼容性
impl Serialize for Folder {
    fn serialize<S>(&self, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        use serde::ser::SerializeStruct;

        let mut state = serializer.serialize_struct("Folder", 4)?;
        state.serialize_field("name", &self.name)?;
        state.serialize_field("path", &self.path)?;
        state.serialize_field("papers_count", &self.papers_count)?;

        // 将 HashMap 转换为排序的 Vec
        let subfolders_vec = self.get_subfolders_list();
        state.serialize_field("subfolders", &subfolders_vec)?;

        state.end()
    }
}

// 自定义反序列化实现，将 Vec 转换为 HashMap
fn deserialize_subfolders<'de, D>(deserializer: D) -> Result<HashMap<String, Folder>, D::Error>
where
    D: Deserializer<'de>,
{
    let subfolders_vec: Vec<Folder> = Vec::deserialize(deserializer)?;
    let mut subfolders_map = HashMap::new();

    for folder in subfolders_vec {
        subfolders_map.insert(folder.name.clone(), folder);
    }

    Ok(subfolders_map)
}

#[derive(Debug, Deserialize)]
pub struct CreateFolderRequest {
    pub name: String,
    pub parent_path: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct RenameFolderRequest {
    pub new_name: String,
}

impl Folder {
    pub fn new(name: String, path: String) -> Self {
        Self {
            name,
            path,
            papers_count: 0,
            subfolders: HashMap::new(),
        }
    }

    /// 从物理路径创建Folder（转换为虚拟路径）
    pub fn from_physical_path(name: String, physical_path: PathBuf) -> Self {
        let config = crate::config::get_config();
        let papers_dir = PathBuf::from(&config.storage.papers_directory);

        let virtual_path = if let Ok(relative_path) = physical_path.strip_prefix(&papers_dir) {
            relative_path.to_string_lossy().to_string()
        } else {
            name.clone()
        };

        Self {
            name,
            path: virtual_path,
            papers_count: 0,
            subfolders: HashMap::new(),
        }
    }

    /// 获取子文件夹列表（转换为Vec以保持API兼容性）
    pub fn get_subfolders_list(&self) -> Vec<Folder> {
        let mut folders: Vec<Folder> = self.subfolders.values().cloned().collect();
        folders.sort_by(|a, b| a.name.cmp(&b.name));
        folders
    }

    /// 添加子文件夹
    pub fn add_subfolder(&mut self, folder: Folder) {
        self.subfolders.insert(folder.name.clone(), folder);
    }

    /// 获取指定名称的子文件夹
    pub fn get_subfolder(&self, name: &str) -> Option<&Folder> {
        self.subfolders.get(name)
    }

    /// 获取指定名称的子文件夹（可变引用）
    pub fn get_subfolder_mut(&mut self, name: &str) -> Option<&mut Folder> {
        self.subfolders.get_mut(name)
    }
}

/// 文件夹删除结果
#[derive(Debug, Serialize)]
pub struct FolderDeleteResult {
    /// 删除的文件夹路径
    pub folder_path: String,
    /// 删除的文献数量
    pub deleted_papers_count: usize,
    /// 删除的文献ID列表
    pub deleted_paper_ids: Vec<Uuid>,
    /// 是否为强制删除
    pub force_delete: bool,
}
