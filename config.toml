# Lite Papers 配置文件

[server]
host = "127.0.0.1"
port = 3000
name = "Lite Papers"
description = "轻量级科研文献管理系统"

[storage]
papers_directory = "papers"
allowed_image_formats = ["jpg", "jpeg", "png", "gif", "bmp", "webp", "svg"]
allowed_document_formats = ["pdf", "txt", "md", "json"]

[pagination]
default_page_size = 10
max_page_size = 100
min_page_size = 1

[search]
default_sort_by = "created_at"
default_sort_order = "desc"
min_word_length = 2

[stats]
top_authors_limit = 10
top_keywords_limit = 20
recent_papers_limit = 10

[cache]
verbose_logging = true
