# Lite Papers - 轻量级科研文献管理系统

基于 Rust Salvo 框架的轻量级科研文献管理软件后端服务。

## 特性

- 📁 **文件夹管理**：依托操作系统的文件夹结构
- 📄 **文献管理**：支持文献元数据存储和管理
- 🗂️ **文件组织**：每篇文献有独立文件夹，包含元数据、原文和附件
- 🚫 **无数据库**：完全基于文件系统，无需数据库配置

## 文件夹结构

```
papers/
├── folder1/
│   ├── paper1_uuid/
│   │   ├── meta.json      # 文献元数据
│   │   ├── origin.pdf     # 文献原文
│   │   ├── images/        # 图片附件
│   │   └── notes/         # 笔记附件
│   └── paper2_uuid/
└── folder2/
```

## 快速开始

### 启动服务器

```bash
cargo run
```

服务器将在 `http://127.0.0.1:3000` 启动。

### API 端点

#### 文件夹管理

- `GET /api/folders` - 获取文件夹列表
- `POST /api/folders` - 创建文件夹
- `DELETE /api/folders/{path}` - 删除文件夹
- `PUT /api/folders/{path}` - 重命名文件夹

#### 文献管理

- `GET /api/papers` - 获取文献列表
- `POST /api/papers` - 添加文献
- `GET /api/papers/{id}` - 获取文献详情
- `PUT /api/papers/{id}` - 更新文献
- `DELETE /api/papers/{id}` - 删除文献
- `GET /api/papers/{id}/files` - 获取文献附件列表
- `POST /api/papers/{id}/files` - 上传附件
- `GET /api/papers/{id}/files/{filename}` - 下载附件
- `GET /api/papers/{id}/files/preview/{filename}` - 预览附件

## API 使用示例

### 创建文件夹

```bash
curl -X POST http://127.0.0.1:3000/api/folders \
  -H "Content-Type: application/json" \
  -d '{"name": "AI Papers", "parent_path": null}'
```

### 添加文献

```bash
curl -X POST http://127.0.0.1:3000/api/papers \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Attention Is All You Need",
    "authors": ["Ashish Vaswani", "Noam Shazeer"],
    "journal": "NIPS",
    "year": 2017,
    "doi": "10.48550/arXiv.1706.03762",
    "abstract_text": "The dominant sequence transduction models are based on complex recurrent or convolutional neural networks.",
    "keywords": ["transformer", "attention", "neural networks"],
    "folder_path": "AI Papers"
  }'
```

### 获取文献列表

```bash
# 默认分页（第1页，每页10条）
curl http://127.0.0.1:3000/api/papers

# 指定分页参数
curl "http://127.0.0.1:3000/api/papers?page=1&page_size=5"

# 按文件夹过滤
curl "http://127.0.0.1:3000/api/papers?folder=AI%20Papers&page=1&page_size=10"
```

### 获取特定文献

```bash
curl http://127.0.0.1:3000/api/papers/{paper_id}
```

### 上传文件

```bash
# 上传笔记文件
curl -X POST "http://127.0.0.1:3000/api/papers/{paper_id}/files?type=note" \
  -F "file=@your_note.txt"

# 上传图片文件
curl -X POST "http://127.0.0.1:3000/api/papers/{paper_id}/files?type=image" \
  -F "file=@your_image.png"

# 上传原文PDF
curl -X POST "http://127.0.0.1:3000/api/papers/{paper_id}/files?type=origin" \
  -F "file=@paper.pdf"
```

### 获取文献附件列表

```bash
curl http://127.0.0.1:3000/api/papers/{paper_id}/files
```

### 下载附件

```bash
# 下载原文PDF
curl -O http://127.0.0.1:3000/api/papers/{paper_id}/files/origin.pdf

# 下载图片文件
curl -O http://127.0.0.1:3000/api/papers/{paper_id}/files/image.png

# 下载笔记文件
curl -O http://127.0.0.1:3000/api/papers/{paper_id}/files/note.txt
```

### 预览附件

```bash
# 预览图片（在浏览器中直接显示）
curl http://127.0.0.1:3000/api/papers/{paper_id}/files/preview/image.png

# 预览文本文件
curl http://127.0.0.1:3000/api/papers/{paper_id}/files/preview/note.txt

# 预览PDF文件
curl http://127.0.0.1:3000/api/papers/{paper_id}/files/preview/origin.pdf
```

## 数据结构

### API 响应格式

所有API响应都遵循统一的格式：

```json
{
  "code": 200,
  "message": "Success",
  "data": { /* 实际数据 */ }
}
```

**成功响应示例（文件夹列表）：**
```json
{
  "code": 200,
  "message": "Success",
  "data": [
    {
      "name": "AI Papers",
      "path": "papers\\AI Papers",
      "papers_count": 1,
      "subfolders": []
    }
  ]
}
```

**成功响应示例（分页文献列表）：**
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "papers": [
      {
        "id": "46ff3c2e-f3a4-474d-af68-56d61f2e4f7c",
        "title": "Attention Is All You Need",
        "authors": ["Ashish Vaswani", "Noam Shazeer"],
        "journal": "NIPS",
        "year": 2017,
        "doi": "10.48550/arXiv.1706.03762",
        "abstract_text": "The dominant sequence transduction models...",
        "keywords": ["transformer", "attention", "neural networks"],
        "folder_path": "AI Papers",
        "created_at": "2025-06-10T06:33:03.470935100Z",
        "updated_at": "2025-06-10T06:33:03.470935100Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 10,
      "total": 3,
      "total_pages": 1,
      "has_next": false,
      "has_prev": false
    }
  }
}
```

**错误响应示例：**
```json
{
  "code": 404,
  "message": "Paper not found",
  "data": null
}
```

### 文献元数据 (meta.json)

```json
{
  "id": "46ff3c2e-f3a4-474d-af68-56d61f2e4f7c",
  "title": "Attention Is All You Need",
  "authors": ["Ashish Vaswani", "Noam Shazeer"],
  "journal": "NIPS",
  "year": 2017,
  "doi": "10.48550/arXiv.1706.03762",
  "abstract_text": "The dominant sequence transduction models...",
  "keywords": ["transformer", "attention", "neural networks"],
  "folder_path": "AI Papers",
  "created_at": "2025-06-10T06:33:03.470935100Z",
  "updated_at": "2025-06-10T06:33:03.470935100Z"
}
```

## 技术栈

- **Rust** - 系统编程语言
- **Salvo** - Web 框架
- **Serde** - 序列化/反序列化
- **UUID** - 唯一标识符生成
- **Chrono** - 日期时间处理
- **Tokio** - 异步运行时

## 分页功能

### 分页参数

- `page`: 页码（从1开始，默认为1）
- `page_size`: 每页大小（默认为10，最小为1，最大为100）

### 分页响应格式

```json
{
  "papers": [...],
  "pagination": {
    "page": 1,
    "page_size": 10,
    "total": 25,
    "total_pages": 3,
    "has_next": true,
    "has_prev": false
  }
}
```

### 分页字段说明

- `page`: 当前页码
- `page_size`: 每页大小
- `total`: 总记录数
- `total_pages`: 总页数
- `has_next`: 是否有下一页
- `has_prev`: 是否有上一页

## 开发状态

- ✅ 文件夹管理
- ✅ 文献CRUD操作
- ✅ 文件列表查看
- ✅ 文件上传功能
- ✅ 文件下载功能
- ✅ 文件预览功能
- ✅ 分页功能
- ⏳ 搜索功能（待实现）

## 文件上传说明

### 支持的文件类型

- **image**: 图片文件（jpg, jpeg, png, gif, bmp, webp, svg）
- **note**: 笔记文件（任意文本文件）
- **origin**: 原文PDF文件（必须是PDF格式）

### 上传参数

- `type`: 文件类型（image/note/origin）
- `file`: 要上传的文件（multipart/form-data）

### 响应格式

成功上传后返回文件信息：

```json
{
  "code": 200,
  "message": "Success",
  "data": {
    "name": "test.png",
    "file_type": "Image",
    "path": "papers\\AI Papers\\46ff3c2e-f3a4-474d-af68-56d61f2e4f7c\\images\\test.png",
    "size": 97,
    "created_at": "2025-06-10T06:50:59.123456Z"
  }
}
```

## 文件下载与预览功能

### 支持的文件操作

- **下载**：支持下载所有类型的附件文件
- **预览**：支持在线预览图片、文本和PDF文件

### 下载功能

**端点**：`GET /api/papers/{id}/files/{filename}`

**特性**：
- **流式响应**：支持大文件下载，避免内存占用过高
- 自动设置正确的 MIME 类型
- 设置 `Content-Disposition: attachment` 强制下载
- 支持所有上传的文件类型
- 安全的文件路径验证
- 低延迟响应，即使对于大文件

### 预览功能

**端点**：`GET /api/papers/{id}/files/preview/{filename}`

**支持的文件类型**：
- **图片**：jpg, jpeg, png, gif, bmp, webp, svg
- **文本**：txt, md, json
- **文档**：pdf

**特性**：
- **流式响应**：支持大文件预览，提供流畅的用户体验
- 设置 `Content-Disposition: inline` 在浏览器中直接显示
- 自动 MIME 类型检测
- 仅支持可安全预览的文件类型
- 实时流式传输，无需等待完整文件加载

### 安全特性

- **路径验证**：防止路径遍历攻击
- **文件名清理**：自动清理不安全的文件名字符
- **类型检查**：验证文件是否可以预览
- **权限控制**：只能访问指定文献的附件

### 错误处理

- `404`：文献或文件不存在
- `400`：文件名无效或文件不支持预览
- `500`：文件读取失败

## 许可证

MIT License
