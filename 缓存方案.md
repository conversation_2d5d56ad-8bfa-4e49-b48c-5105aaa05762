# 缓存方案

## 设计理念

采用全量内存缓存策略，启动时将所有文献数据加载到内存，所有查询操作直接在内存中进行，实现毫秒级响应和强大的检索能力。

## 核心架构

### 缓存存储结构
```
PaperCache {
    papers: HashMap<Uuid, Paper>               // 主数据存储
    folder_index: HashMap<String, Vec<Uuid>>   // 文件夹索引
    author_index: HashMap<String, Vec<Uuid>>   // 作者索引
    keyword_index: HashMap<String, Vec<Uuid>>  // 关键词索引
    title_index: HashMap<String, Vec<Uuid>>    // 标题索引
    abstract_index: HashMap<String, Vec<Uuid>> // 摘要索引
    folders: HashMap<String, Folder>           // 文件夹缓存
}
```

### 生命周期管理

**启动阶段**：
- 扫描papers目录，读取所有meta.json文件
- 构建Paper对象并加载到内存
- 建立各类索引以加速查询

**运行阶段**：
- 所有查询操作直接访问内存数据
- 写操作同时更新内存缓存和磁盘文件
- 无过期机制，数据常驻内存

## 功能特性

### 基础查询
- 按ID获取单个文献
- 按文件夹获取文献列表
- 分页查询支持

### 搜索功能
- 标题关键词搜索
- 摘要内容搜索
- 作者姓名搜索
- 关键词标签搜索
- 复合条件搜索
- 模糊匹配搜索

### 统计分析
- 文献总数统计
- 按文件夹分组统计
- 按年份分布统计
- 热门作者和关键词
- 最近添加的文献

## 性能优势

### 查询性能
- Paper查询：O(1) 直接访问
- 索引查询：O(1) 快速定位
- 复杂搜索：内存操作，毫秒级响应
- 分页查询：内存排序，无IO开销

### 资源消耗
- 内存使用：约5KB/篇文献
- 启动时间：1000篇约1-2秒
- 索引开销：约30%额外内存

## 数据一致性

### 写操作流程
1. 更新内存缓存数据
2. 重建相关索引
3. 同步写入磁盘文件

### 一致性保证
- 写操作原子性：先磁盘后缓存
- 索引同步更新：保持数据一致
- 启动时完整性检查

## 扩展能力

### 高级检索
- 支持复合条件组合
- 年份范围过滤
- 多字段排序
- 标题和摘要全文检索
- 相关文献推荐

### 实时统计
- 动态文件夹树构建
- 实时数据分析
- 趋势统计展示

## 实现优先级

**Phase 1**：基础缓存加载、简单查询、写时更新
**Phase 2**：索引构建、快速搜索、分页优化  
**Phase 3**：复合搜索、高级功能

## 技术要点

- 使用读写锁保证线程安全
- 启动时异步加载提升体验
- 索引策略平衡内存和性能
- 简单直接的架构设计
