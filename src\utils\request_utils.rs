use salvo::prelude::*;
use crate::error::AppError;

/// 分页参数结构体
#[derive(Debug, <PERSON>lone)]
pub struct PaginationParams {
    pub page: u32,
    pub page_size: u32,
}

impl PaginationParams {
    /// 从请求中提取并验证分页参数
    pub fn from_request(req: &Request) -> Self {
        let config = crate::config::get_config();
        
        let page = req.query::<u32>("page").unwrap_or(1);
        let page_size = req.query::<u32>("page_size").unwrap_or(config.pagination.default_page_size);
        
        // 验证分页参数
        let page = page.max(1);
        let page_size = page_size
            .min(config.pagination.max_page_size)
            .max(config.pagination.min_page_size);
        
        Self { page, page_size }
    }
}

/// 从请求中安全地获取路径参数
pub fn get_path_param(req: &Request, param_name: &str) -> Result<String, AppError> {
    let value = req.param::<String>(param_name).unwrap_or_default();
    if value.is_empty() {
        return Err(AppError::business_logic(&format!("{} is required", param_name)));
    }
    Ok(value)
}

/// 从请求中安全地解析JSON
pub async fn parse_json_body<T>(req: &mut Request) -> Result<T, AppError>
where
    T: serde::de::DeserializeOwned,
{
    req.parse_json::<T>().await
        .map_err(|e| AppError::request_parse(format!("Invalid JSON: {}", e)))
}

/// 验证文件类型参数
pub fn validate_file_type(file_type: &str) -> Result<(), AppError> {
    if !matches!(file_type, "image" | "note" | "origin") {
        return Err(AppError::invalid_file_type(file_type));
    }
    Ok(())
}
